﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:2.0.0.0
//      Reqnroll Generator Version:2.0.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "live-scoring")]
    public partial class LiveScoringFeature : object, Xunit.IClassFixture<LiveScoringFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "live-scoring"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Live Scoring", "  As a putt-putt player\r\n  I want to enter and track scores during the game\r\n  So" +
                " that I can see real-time standings and progress", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "LiveScoring.feature"
#line hidden
        
        public LiveScoringFeature(LiveScoringFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("I have started a game with players \"John Doe\" and \"Jane Smith\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("the game is set for 9 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 10
    await testRunner.AndAsync("I am on the scoring screen for hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Enter score for current player")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Enter score for current player")]
        [Xunit.TraitAttribute("Category", "smoke")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task EnterScoreForCurrentPlayer()
        {
            string[] tagsOfScenario = new string[] {
                    "smoke",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Enter score for current player", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 13
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 14
    await testRunner.GivenAsync("it is \"John Doe\"\'s turn on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 15
    await testRunner.WhenAsync("I tap the score button \"3\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 16
    await testRunner.ThenAsync("\"John Doe\"\'s score for hole 1 should be recorded as 3", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 17
    await testRunner.AndAsync("it should become \"Jane Smith\"\'s turn", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
    await testRunner.AndAsync("the current player indicator should show \"Jane Smith\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Navigate through all players on a hole")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Navigate through all players on a hole")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task NavigateThroughAllPlayersOnAHole()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Navigate through all players on a hole", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 21
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 22
    await testRunner.GivenAsync("it is \"John Doe\"\'s turn on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 23
    await testRunner.WhenAsync("I enter score \"2\" for \"John Doe\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 24
    await testRunner.AndAsync("I enter score \"4\" for \"Jane Smith\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 25
    await testRunner.ThenAsync("both players should have scores recorded for hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 26
    await testRunner.AndAsync("the game should advance to hole 2", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 27
    await testRunner.AndAsync("it should be \"John Doe\"\'s turn on hole 2", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View real-time leaderboard")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "View real-time leaderboard")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewReal_TimeLeaderboard()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View real-time leaderboard", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 30
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 31
    await testRunner.GivenAsync("\"John Doe\" has completed holes 1-3 with scores \"2, 3, 4\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 32
    await testRunner.AndAsync("\"Jane Smith\" has completed holes 1-3 with scores \"3, 2, 3\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 33
    await testRunner.WhenAsync("I tap \"View Leaderboard\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 34
    await testRunner.ThenAsync("I should see \"Jane Smith\" in 1st place with total score 8", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 35
    await testRunner.AndAsync("I should see \"John Doe\" in 2nd place with total score 9", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 36
    await testRunner.AndAsync("I should see the score difference \"+1\" for \"John Doe\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Correct a previously entered score")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Correct a previously entered score")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task CorrectAPreviouslyEnteredScore()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Correct a previously entered score", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 39
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 40
    await testRunner.GivenAsync("\"John Doe\" has a score of 4 on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 41
    await testRunner.AndAsync("I am currently on hole 2", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 42
    await testRunner.WhenAsync("I tap \"Previous Hole\" to go back to hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 43
    await testRunner.AndAsync("I tap on \"John Doe\"\'s score", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 44
    await testRunner.AndAsync("I change the score from 4 to 3", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 45
    await testRunner.AndAsync("I confirm the change", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 46
    await testRunner.ThenAsync("\"John Doe\"\'s score for hole 1 should be updated to 3", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 47
    await testRunner.AndAsync("the total score should be recalculated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Undo last score entry")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Undo last score entry")]
        [Xunit.TraitAttribute("Category", "undo")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task UndoLastScoreEntry()
        {
            string[] tagsOfScenario = new string[] {
                    "undo",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Undo last score entry", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 50
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 51
    await testRunner.GivenAsync("\"John Doe\" has just entered a score of 5 on hole 3", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 52
    await testRunner.WhenAsync("I tap the \"Undo\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 53
    await testRunner.ThenAsync("\"John Doe\"\'s score for hole 3 should be removed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 54
    await testRunner.AndAsync("it should be \"John Doe\"\'s turn again on hole 3", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 55
    await testRunner.AndAsync("the undo button should be disabled", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Apply maximum stroke limit per hole")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Apply maximum stroke limit per hole")]
        [Xunit.TraitAttribute("Category", "stroke-limit")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ApplyMaximumStrokeLimitPerHole()
        {
            string[] tagsOfScenario = new string[] {
                    "stroke-limit",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Apply maximum stroke limit per hole", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 58
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 59
    await testRunner.GivenAsync("the maximum stroke limit is set to 6", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 60
    await testRunner.AndAsync("it is \"John Doe\"\'s turn on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 61
    await testRunner.WhenAsync("I tap the score buttons to reach 6 strokes", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 62
    await testRunner.ThenAsync("\"John Doe\"\'s score should be capped at 6", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 63
    await testRunner.AndAsync("the score entry should automatically advance to the next player", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 64
    await testRunner.AndAsync("I should see a message \"Maximum strokes reached\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Navigate between holes using swipe gestures")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Navigate between holes using swipe gestures")]
        [Xunit.TraitAttribute("Category", "navigation")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task NavigateBetweenHolesUsingSwipeGestures()
        {
            string[] tagsOfScenario = new string[] {
                    "navigation",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Navigate between holes using swipe gestures", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 67
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 68
    await testRunner.GivenAsync("I am on hole 3 with scores entered", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 69
    await testRunner.WhenAsync("I swipe left on the screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 70
    await testRunner.ThenAsync("I should navigate to hole 4", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 71
    await testRunner.WhenAsync("I swipe right on the screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 72
    await testRunner.ThenAsync("I should navigate back to hole 3", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View scores per hole for all players")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "View scores per hole for all players")]
        [Xunit.TraitAttribute("Category", "per-hole-view")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewScoresPerHoleForAllPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "per-hole-view",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View scores per hole for all players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 75
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 76
    await testRunner.GivenAsync("the game is in progress with multiple holes completed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 77
    await testRunner.WhenAsync("I tap \"Hole-by-Hole View\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 78
    await testRunner.ThenAsync("I should see a grid showing all players\' scores for each completed hole", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 79
    await testRunner.AndAsync("I should see the par value for each hole", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 80
    await testRunner.AndAsync("I should see each player\'s score relative to par (e.g., +1, -1, E)", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot enter invalid score")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Cannot enter invalid score")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotEnterInvalidScore()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot enter invalid score", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 83
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 84
    await testRunner.GivenAsync("it is \"John Doe\"\'s turn on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 85
    await testRunner.WhenAsync("I try to enter a score of 0", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 86
    await testRunner.ThenAsync("I should see an error message \"Score must be at least 1\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 87
    await testRunner.AndAsync("the score should not be recorded", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot proceed without entering all scores for current hole")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Cannot proceed without entering all scores for current hole")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotProceedWithoutEnteringAllScoresForCurrentHole()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot proceed without entering all scores for current hole", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 90
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 91
    await testRunner.GivenAsync("it is \"John Doe\"\'s turn on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 92
    await testRunner.AndAsync("\"John Doe\" has entered a score", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 93
    await testRunner.AndAsync("\"Jane Smith\" has not entered a score", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 94
    await testRunner.WhenAsync("I try to navigate to the next hole", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 95
    await testRunner.ThenAsync("I should see a message \"All players must complete the current hole\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 96
    await testRunner.AndAsync("I should remain on hole 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Handle hole-in-one celebration")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Handle hole-in-one celebration")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        public async global::System.Threading.Tasks.Task HandleHole_In_OneCelebration()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Handle hole-in-one celebration", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 99
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 100
    await testRunner.GivenAsync("it is \"Jane Smith\"\'s turn on hole 5", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 101
    await testRunner.WhenAsync("I enter a score of 1", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 102
    await testRunner.ThenAsync("I should see a \"Hole-in-One!\" celebration message", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 103
    await testRunner.AndAsync("there should be a special animation or sound effect", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 104
    await testRunner.AndAsync("the score should be recorded normally", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Complete final hole and finish game")]
        [Xunit.TraitAttribute("FeatureTitle", "Live Scoring")]
        [Xunit.TraitAttribute("Description", "Complete final hole and finish game")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        public async global::System.Threading.Tasks.Task CompleteFinalHoleAndFinishGame()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Complete final hole and finish game", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 107
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 108
    await testRunner.GivenAsync("I am on hole 9 (the final hole)", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 109
    await testRunner.AndAsync("all players have entered their scores for hole 9", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 110
    await testRunner.WhenAsync("the last player\'s score is entered", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 111
    await testRunner.ThenAsync("the game should be marked as complete", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 112
    await testRunner.AndAsync("I should be taken to the final scorecard screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 113
    await testRunner.AndAsync("the game should be automatically saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await LiveScoringFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await LiveScoringFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
