﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:2.0.0.0
//      Reqnroll Generator Version:2.0.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "game-history")]
    public partial class GameHistoryAndStatisticsFeature : object, Xunit.IClassFixture<GameHistoryAndStatisticsFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "game-history"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Game History and Statistics", "  As a putt-putt player\r\n  I want to view my game history and statistics\r\n  So th" +
                "at I can track my progress and review past performances", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "GameHistory.feature"
#line hidden
        
        public GameHistoryAndStatisticsFeature(GameHistoryAndStatisticsFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("the app is launched", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("I have completed several games in the past", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View list of completed games")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View list of completed games")]
        [Xunit.TraitAttribute("Category", "smoke")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewListOfCompletedGames()
        {
            string[] tagsOfScenario = new string[] {
                    "smoke",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View list of completed games", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 12
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 13
    await testRunner.GivenAsync("I have completed games on different dates", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 14
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 15
    await testRunner.WhenAsync("I tap \"Game History\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 16
    await testRunner.ThenAsync("I should see a list of all completed games", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 17
    await testRunner.AndAsync("each game should show the date, course name, and players", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
    await testRunner.AndAsync("the games should be sorted by date (most recent first)", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View detailed scorecard of a completed game")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View detailed scorecard of a completed game")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewDetailedScorecardOfACompletedGame()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View detailed scorecard of a completed game", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 21
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 22
    await testRunner.GivenAsync("I have a completed game from \"2025-07-15\" with players \"John\" and \"Jane\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 23
    await testRunner.AndAsync("I am on the game history screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 24
    await testRunner.WhenAsync("I tap on the game from \"2025-07-15\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 25
    await testRunner.ThenAsync("I should see the complete scorecard", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 26
    await testRunner.AndAsync("I should see each player\'s score for every hole", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 27
    await testRunner.AndAsync("I should see the final totals and rankings", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 28
    await testRunner.AndAsync("I should see the course name and par values", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Save game progress automatically")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Save game progress automatically")]
        [Xunit.TraitAttribute("Category", "save-load")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task SaveGameProgressAutomatically()
        {
            string[] tagsOfScenario = new string[] {
                    "save-load",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Save game progress automatically", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 31
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 32
    await testRunner.GivenAsync("I have started a new game", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 33
    await testRunner.AndAsync("I have entered scores for 5 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 34
    await testRunner.WhenAsync("I close the app", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 35
    await testRunner.AndAsync("I reopen the app", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 36
    await testRunner.ThenAsync("I should see an option to \"Resume Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 37
    await testRunner.AndAsync("the game should continue from hole 6", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 38
    await testRunner.AndAsync("all previously entered scores should be preserved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Resume an unfinished game")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Resume an unfinished game")]
        [Xunit.TraitAttribute("Category", "save-load")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ResumeAnUnfinishedGame()
        {
            string[] tagsOfScenario = new string[] {
                    "save-load",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Resume an unfinished game", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 41
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 42
    await testRunner.GivenAsync("I have an unfinished game saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 43
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 44
    await testRunner.WhenAsync("I tap \"Resume Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 45
    await testRunner.ThenAsync("I should be taken to the scoring screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 46
    await testRunner.AndAsync("I should be on the correct hole where I left off", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 47
    await testRunner.AndAsync("all previous scores should be displayed correctly", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Save completed game manually")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Save completed game manually")]
        [Xunit.TraitAttribute("Category", "save-load")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task SaveCompletedGameManually()
        {
            string[] tagsOfScenario = new string[] {
                    "save-load",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Save completed game manually", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 50
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 51
    await testRunner.GivenAsync("I have just finished a game", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 52
    await testRunner.AndAsync("I am on the final scorecard screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 53
    await testRunner.WhenAsync("I tap \"Save Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 54
    await testRunner.ThenAsync("the game should be saved to game history", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 55
    await testRunner.AndAsync("I should see a confirmation \"Game saved successfully\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 56
    await testRunner.AndAsync("the game should appear in the game history list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View player statistics overview")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View player statistics overview")]
        [Xunit.TraitAttribute("Category", "statistics")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewPlayerStatisticsOverview()
        {
            string[] tagsOfScenario = new string[] {
                    "statistics",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View player statistics overview", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 59
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 60
    await testRunner.GivenAsync("I have completed multiple games as \"John Doe\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 61
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 62
    await testRunner.WhenAsync("I tap \"Statistics\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 63
    await testRunner.AndAsync("I select \"John Doe\" from the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 64
    await testRunner.ThenAsync("I should see \"John Doe\"\'s overall statistics", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 65
    await testRunner.AndAsync("I should see the average score per game", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 66
    await testRunner.AndAsync("I should see the best score achieved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 67
    await testRunner.AndAsync("I should see the total number of games played", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View hole-in-one achievements")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View hole-in-one achievements")]
        [Xunit.TraitAttribute("Category", "statistics")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewHole_In_OneAchievements()
        {
            string[] tagsOfScenario = new string[] {
                    "statistics",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View hole-in-one achievements", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 70
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 71
    await testRunner.GivenAsync("\"Jane Smith\" has achieved 3 hole-in-ones in past games", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 72
    await testRunner.AndAsync("I am on the statistics screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 73
    await testRunner.WhenAsync("I select \"Jane Smith\" from the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 74
    await testRunner.ThenAsync("I should see \"Hole-in-ones: 3\" in the statistics", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 75
    await testRunner.AndAsync("I should be able to tap to see details of each hole-in-one", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 76
    await testRunner.AndAsync("each hole-in-one should show the date, course, and hole number", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View best scores by course")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View best scores by course")]
        [Xunit.TraitAttribute("Category", "statistics")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewBestScoresByCourse()
        {
            string[] tagsOfScenario = new string[] {
                    "statistics",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View best scores by course", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 79
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 80
    await testRunner.GivenAsync("\"John Doe\" has played \"Adventure Course\" multiple times", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 81
    await testRunner.AndAsync("I am viewing \"John Doe\"\'s statistics", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 82
    await testRunner.WhenAsync("I tap \"Best Scores by Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 83
    await testRunner.ThenAsync("I should see the best score for \"Adventure Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 84
    await testRunner.AndAsync("I should see the date when the best score was achieved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 85
    await testRunner.AndAsync("I should see scores for all courses played", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View scoring trends over time")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View scoring trends over time")]
        [Xunit.TraitAttribute("Category", "statistics")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewScoringTrendsOverTime()
        {
            string[] tagsOfScenario = new string[] {
                    "statistics",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View scoring trends over time", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 88
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 89
    await testRunner.GivenAsync("\"Jane Smith\" has played games over the past 6 months", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 90
    await testRunner.AndAsync("I am viewing \"Jane Smith\"\'s statistics", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 91
    await testRunner.WhenAsync("I tap \"Scoring Trends\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 92
    await testRunner.ThenAsync("I should see a graph showing score improvements over time", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 93
    await testRunner.AndAsync("I should see the average score per month", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 94
    await testRunner.AndAsync("I should be able to see if performance is improving or declining", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Filter game history by date range")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Filter game history by date range")]
        [Xunit.TraitAttribute("Category", "filtering")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task FilterGameHistoryByDateRange()
        {
            string[] tagsOfScenario = new string[] {
                    "filtering",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Filter game history by date range", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 97
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 98
    await testRunner.GivenAsync("I have games from the past year", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 99
    await testRunner.AndAsync("I am on the game history screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 100
    await testRunner.WhenAsync("I tap \"Filter\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 101
    await testRunner.AndAsync("I select \"Last 30 days\" as the date range", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 102
    await testRunner.ThenAsync("I should only see games from the last 30 days", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 103
    await testRunner.AndAsync("the filter should be clearly indicated at the top", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Filter game history by course")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Filter game history by course")]
        [Xunit.TraitAttribute("Category", "filtering")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task FilterGameHistoryByCourse()
        {
            string[] tagsOfScenario = new string[] {
                    "filtering",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Filter game history by course", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 106
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 107
    await testRunner.GivenAsync("I have played games on \"Main Course\" and \"Adventure Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 108
    await testRunner.AndAsync("I am on the game history screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 109
    await testRunner.WhenAsync("I tap \"Filter\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 110
    await testRunner.AndAsync("I select \"Adventure Course\" from the course filter", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 111
    await testRunner.ThenAsync("I should only see games played on \"Adventure Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 112
    await testRunner.AndAsync("the course filter should be clearly indicated", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Search game history by player name")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Search game history by player name")]
        [Xunit.TraitAttribute("Category", "search")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task SearchGameHistoryByPlayerName()
        {
            string[] tagsOfScenario = new string[] {
                    "search",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Search game history by player name", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 115
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 116
    await testRunner.GivenAsync("I have games with various players", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 117
    await testRunner.AndAsync("I am on the game history screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 118
    await testRunner.WhenAsync("I tap the search icon", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 119
    await testRunner.AndAsync("I enter \"John\" in the search field", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 120
    await testRunner.ThenAsync("I should see only games that included a player named \"John\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 121
    await testRunner.AndAsync("the search term should be highlighted in the results", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Maintain game history across app updates")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Maintain game history across app updates")]
        [Xunit.TraitAttribute("Category", "data-persistence")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task MaintainGameHistoryAcrossAppUpdates()
        {
            string[] tagsOfScenario = new string[] {
                    "data-persistence",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Maintain game history across app updates", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 124
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 125
    await testRunner.GivenAsync("I have 20 completed games in my history", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 126
    await testRunner.WhenAsync("the app is updated to a new version", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 127
    await testRunner.AndAsync("I open the updated app", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 128
    await testRunner.ThenAsync("all 20 games should still be in my history", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 129
    await testRunner.AndAsync("all game details should be preserved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 130
    await testRunner.AndAsync("statistics should be calculated correctly", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Handle corrupted game data gracefully")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "Handle corrupted game data gracefully")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task HandleCorruptedGameDataGracefully()
        {
            string[] tagsOfScenario = new string[] {
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Handle corrupted game data gracefully", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 133
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 134
    await testRunner.GivenAsync("I have a game with corrupted data in the history", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 135
    await testRunner.WhenAsync("I try to view that game\'s details", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 136
    await testRunner.ThenAsync("I should see an error message \"Unable to load game details\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 137
    await testRunner.AndAsync("the app should not crash", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 138
    await testRunner.AndAsync("other games should still be accessible", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View statistics with no completed games")]
        [Xunit.TraitAttribute("FeatureTitle", "Game History and Statistics")]
        [Xunit.TraitAttribute("Description", "View statistics with no completed games")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        public async global::System.Threading.Tasks.Task ViewStatisticsWithNoCompletedGames()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View statistics with no completed games", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 141
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 142
    await testRunner.GivenAsync("I am a new user with no completed games", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 143
    await testRunner.WhenAsync("I tap \"Statistics\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 144
    await testRunner.ThenAsync("I should see a message \"No games completed yet\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 145
    await testRunner.AndAsync("I should see an option to \"Start Your First Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 146
    await testRunner.AndAsync("no statistics should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await GameHistoryAndStatisticsFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await GameHistoryAndStatisticsFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
