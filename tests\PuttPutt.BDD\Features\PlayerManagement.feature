@player-management
Feature: Player Management
  As a putt-putt player
  I want to manage player profiles
  So that I can easily set up games with regular players

  Background:
    Given the app is launched
    And I am on the main menu

  @happy-path
  Scenario: Navigate to player management screen
    When I tap on the "Manage Players" option
    Then I should see the player management screen
	And I should see a list of existing players

  @smoke @happy-path
  Scenario: Add a new player profile
    Given I navigate to the player management screen
    When I tap the "Add Player" button
    And I enter "John Doe" as the player name
    And I select a blue color for the player
    And I tap "Save Player"
    Then the player "<PERSON> Do<PERSON>" should be added to the player list
    And the player should have a blue color indicator

  @happy-path
  Scenario: Edit an existing player profile
    Given I have a player "<PERSON>" with a red color
    And I am on the player management screen
    When I tap on the "<PERSON>" player
    And I change the player name to "<PERSON>"
    And I change the color to green
    And I tap "Save Changes"
    Then the player should be updated to "<PERSON>"
    And the player should have a green color indicator

  @happy-path
  Scenario: Delete a player profile
    Given I have a player "<PERSON>" in the player list
    And I am on the player management screen
    When I tap on the "<PERSON>" player
    And I tap "Delete Player"
    And I confirm the deletion
    Then "<PERSON>" should be removed from the player list

  @happy-path
  Scenario: Mark a player as favorite
    Given I have a player "<PERSON>" in the player list
    And I am on the player management screen
    When I tap on the "Alice Brown" player
    And I toggle the "Favorite" option to enabled
    And I tap "Save Changes"
    Then "Alice <PERSON>" should be marked as a favorite player
    And "<PERSON>" should appear at the top of the player list

  @validation @error-handling
  Scenario: Cannot add player with empty name
    Given I am on the player management screen
    When I tap the "Add Player" button
    And I leave the player name field empty
    And I tap "Save Player"
    Then I should see an error message "Player name is required"
    And the player should not be saved

  @validation @error-handling
  Scenario: Cannot add duplicate player names
    Given I have a player "Mike Davis" in the player list
    And I am on the player management screen
    When I tap the "Add Player" button
    And I enter "Mike Davis" as the player name
    And I tap "Save Player"
    Then I should see an error message "A player with this name already exists"
    And the duplicate player should not be saved

  @edge-case
  Scenario: Add player with maximum allowed name length
    Given I am on the player management screen
    When I tap the "Add Player" button
    And I enter a player name with 50 characters
    And I tap "Save Player"
    Then the player should be added successfully
    And the full name should be displayed correctly

  @edge-case @error-handling
  Scenario: Cannot add player with name exceeding maximum length
    Given I am on the player management screen
    When I tap the "Add Player" button
    And I enter a player name with 51 characters
    And I tap "Save Player"
    Then I should see an error message "Player name cannot exceed 50 characters"
    And the player should not be saved
