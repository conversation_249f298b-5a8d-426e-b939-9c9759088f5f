@live-scoring
Feature: Live Scoring
  As a putt-putt player
  I want to enter and track scores during the game
  So that I can see real-time standings and progress

  Background:
    Given I have started a game with players "<PERSON>" and "<PERSON>"
    And the game is set for 9 holes
    And I am on the scoring screen for hole 1

  @smoke @happy-path
  Scenario: Enter score for current player
    Given it is "<PERSON> Doe"'s turn on hole 1
    When I tap the score button "3"
    Then "John Doe"'s score for hole 1 should be recorded as 3
    And it should become "<PERSON>"'s turn
    And the current player indicator should show "<PERSON>"

  @happy-path
  Scenario: Navigate through all players on a hole
    Given it is "<PERSON>"'s turn on hole 1
    When I enter score "2" for "<PERSON>"
    And I enter score "4" for "<PERSON>"
    Then both players should have scores recorded for hole 1
    And the game should advance to hole 2
    And it should be "<PERSON>"'s turn on hole 2

  @happy-path
  Scenario: View real-time leaderboard
    Given "<PERSON> Doe" has completed holes 1-3 with scores "2, 3, 4"
    And "<PERSON>" has completed holes 1-3 with scores "3, 2, 3"
    When I tap "View Leaderboard"
    Then I should see "<PERSON>" in 1st place with total score 8
    And I should see "<PERSON>" in 2nd place with total score 9
    And I should see the score difference "+1" for "<PERSON> Doe"

  @happy-path
  Scenario: Correct a previously entered score
    Given "John Doe" has a score of 4 on hole 1
    And I am currently on hole 2
    When I tap "Previous Hole" to go back to hole 1
    And I tap on "<PERSON> Doe"'s score
    And I change the score from 4 to 3
    And I confirm the change
    Then "<PERSON> Doe"'s score for hole 1 should be updated to 3
    And the total score should be recalculated

  @undo @happy-path
  Scenario: Undo last score entry
    Given "<PERSON> Doe" has just entered a score of 5 on hole 3
    When I tap the "Undo" button
    Then "<PERSON> Doe"'s score for hole 3 should be removed
    And it should be "John Doe"'s turn again on hole 3
    And the undo button should be disabled

  @stroke-limit @happy-path
  Scenario: Apply maximum stroke limit per hole
    Given the maximum stroke limit is set to 6
    And it is "John Doe"'s turn on hole 1
    When I tap the score buttons to reach 6 strokes
    Then "John Doe"'s score should be capped at 6
    And the score entry should automatically advance to the next player
    And I should see a message "Maximum strokes reached"

  @navigation @happy-path
  Scenario: Navigate between holes using swipe gestures
    Given I am on hole 3 with scores entered
    When I swipe left on the screen
    Then I should navigate to hole 4
    When I swipe right on the screen
    Then I should navigate back to hole 3

  @per-hole-view @happy-path
  Scenario: View scores per hole for all players
    Given the game is in progress with multiple holes completed
    When I tap "Hole-by-Hole View"
    Then I should see a grid showing all players' scores for each completed hole
    And I should see the par value for each hole
    And I should see each player's score relative to par (e.g., +1, -1, E)

  @validation @error-handling
  Scenario: Cannot enter invalid score
    Given it is "John Doe"'s turn on hole 1
    When I try to enter a score of 0
    Then I should see an error message "Score must be at least 1"
    And the score should not be recorded

  @validation @error-handling
  Scenario: Cannot proceed without entering all scores for current hole
    Given it is "John Doe"'s turn on hole 1
    And "John Doe" has entered a score
    And "Jane Smith" has not entered a score
    When I try to navigate to the next hole
    Then I should see a message "All players must complete the current hole"
    And I should remain on hole 1

  @edge-case
  Scenario: Handle hole-in-one celebration
    Given it is "Jane Smith"'s turn on hole 5
    When I enter a score of 1
    Then I should see a "Hole-in-One!" celebration message
    And there should be a special animation or sound effect
    And the score should be recorded normally

  @edge-case
  Scenario: Complete final hole and finish game
    Given I am on hole 9 (the final hole)
    And all players have entered their scores for hole 9
    When the last player's score is entered
    Then the game should be marked as complete
    And I should be taken to the final scorecard screen
    And the game should be automatically saved
