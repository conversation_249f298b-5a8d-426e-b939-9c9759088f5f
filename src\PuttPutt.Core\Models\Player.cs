namespace PuttPutt.Core.Models;

/// <summary>
/// Represents a player in the Putt-Putt game
/// </summary>
public class Player
{
    public string Name { get; set; } = string.Empty;
    public string Color { get; set; } = string.Empty;
    public bool IsFavorite { get; set; }

    public Player() { }

    public Player(string name, string color, bool isFavorite)
    {
        Name = name;
        Color = color;
        IsFavorite = isFavorite;
    }

    public override string ToString() => $"{Name} ({Color}) - Favorite: {IsFavorite}";
}
