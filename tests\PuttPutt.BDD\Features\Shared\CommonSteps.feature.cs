﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features.Shared
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "shared")]
    public partial class CommonStepsFeature : object, Xunit.IClassFixture<CommonStepsFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "shared"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/Shared", "Common Steps", "  This feature file contains shared step definitions that are used across multipl" +
                "e feature files.\r\n  These steps should not be executed as standalone scenarios b" +
                "ut serve as reusable components.", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "CommonSteps.feature"
#line hidden
        
        public CommonStepsFeature(CommonStepsFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common application setup steps")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common application setup steps")]
        [Xunit.TraitAttribute("Category", "shared-background")]
        public async global::System.Threading.Tasks.Task CommonApplicationSetupSteps()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-background"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common application setup steps", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 7
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 8
    await testRunner.GivenAsync("the app is launched", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("the app has been initialized with default settings", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 10
    await testRunner.AndAsync("the local database is available", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 11
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common player management steps")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common player management steps")]
        [Xunit.TraitAttribute("Category", "shared-player-setup")]
        public async global::System.Threading.Tasks.Task CommonPlayerManagementSteps()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-player-setup"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common player management steps", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 14
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
                global::Reqnroll.Table table1 = new global::Reqnroll.Table(new string[] {
                            "Name",
                            "Color",
                            "Favorite"});
                table1.AddRow(new string[] {
                            "John Doe",
                            "Blue",
                            "Yes"});
                table1.AddRow(new string[] {
                            "Jane Smith",
                            "Red",
                            "No"});
                table1.AddRow(new string[] {
                            "Bob Johnson",
                            "Green",
                            "No"});
                table1.AddRow(new string[] {
                            "Alice Brown",
                            "Yellow",
                            "Yes"});
#line 15
    await testRunner.GivenAsync("I have the following players in my player list:", ((string)(null)), table1, "Given ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common course setup steps")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common course setup steps")]
        [Xunit.TraitAttribute("Category", "shared-course-setup")]
        public async global::System.Threading.Tasks.Task CommonCourseSetupSteps()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-course-setup"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common course setup steps", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 23
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
                global::Reqnroll.Table table2 = new global::Reqnroll.Table(new string[] {
                            "Course Name",
                            "Holes",
                            "Par Values"});
                table2.AddRow(new string[] {
                            "Main Course",
                            "9",
                            "3,3,4,3,4,3,3,4,3"});
                table2.AddRow(new string[] {
                            "Adventure Course",
                            "18",
                            "3,3,4,3,4,3,3,4,3,4,3,3,4,3,4,3,3,4"});
                table2.AddRow(new string[] {
                            "Mini Course",
                            "6",
                            "3,3,3,3,3,3"});
#line 24
    await testRunner.GivenAsync("I have the following courses available:", ((string)(null)), table2, "Given ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common game setup steps")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common game setup steps")]
        [Xunit.TraitAttribute("Category", "shared-game-setup")]
        public async global::System.Threading.Tasks.Task CommonGameSetupSteps()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-game-setup"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common game setup steps", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 31
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
                global::Reqnroll.Table table3 = new global::Reqnroll.Table(new string[] {
                            "Players",
                            "John Doe, Jane Smith"});
                table3.AddRow(new string[] {
                            "Course",
                            "Main Course"});
                table3.AddRow(new string[] {
                            "Holes",
                            "9"});
                table3.AddRow(new string[] {
                            "Current Hole",
                            "1"});
                table3.AddRow(new string[] {
                            "Current Player",
                            "John Doe"});
#line 32
    await testRunner.GivenAsync("I have started a game with the following configuration:", ((string)(null)), table3, "Given ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common navigation steps")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common navigation steps")]
        [Xunit.TraitAttribute("Category", "shared-navigation")]
        public async global::System.Threading.Tasks.Task CommonNavigationSteps()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-navigation"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common navigation steps", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 40
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
                global::Reqnroll.Table table4 = new global::Reqnroll.Table(new string[] {
                            "Screen Name",
                            "Navigation Path"});
                table4.AddRow(new string[] {
                            "Main Menu",
                            "App Launch"});
                table4.AddRow(new string[] {
                            "Player Management",
                            "Main Menu > Manage Players"});
                table4.AddRow(new string[] {
                            "Course Management",
                            "Main Menu > Manage Courses"});
                table4.AddRow(new string[] {
                            "Game History",
                            "Main Menu > Game History"});
                table4.AddRow(new string[] {
                            "Statistics",
                            "Main Menu > Statistics"});
                table4.AddRow(new string[] {
                            "Game Rules",
                            "Main Menu > Game Rules"});
                table4.AddRow(new string[] {
                            "New Game Setup",
                            "Main Menu > New Game"});
#line 41
    await testRunner.GivenAsync("I can navigate to the following screens:", ((string)(null)), table4, "Given ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common validation scenarios")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common validation scenarios")]
        [Xunit.TraitAttribute("Category", "shared-validation")]
        public async global::System.Threading.Tasks.Task CommonValidationScenarios()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-validation"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common validation scenarios", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 52
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
                global::Reqnroll.Table table5 = new global::Reqnroll.Table(new string[] {
                            "Field Type",
                            "Min Length",
                            "Max Length",
                            "Required",
                            "Special Rules"});
                table5.AddRow(new string[] {
                            "Player Name",
                            "1",
                            "50",
                            "Yes",
                            "No duplicates"});
                table5.AddRow(new string[] {
                            "Course Name",
                            "1",
                            "100",
                            "Yes",
                            "No duplicates"});
                table5.AddRow(new string[] {
                            "Hole Count",
                            "1",
                            "50",
                            "Yes",
                            "Numeric only"});
                table5.AddRow(new string[] {
                            "Score",
                            "1",
                            "10",
                            "Yes",
                            "Numeric only"});
                table5.AddRow(new string[] {
                            "Par Value",
                            "1",
                            "8",
                            "Yes",
                            "Numeric only"});
#line 53
    await testRunner.GivenAsync("the following validation rules apply:", ((string)(null)), table5, "Given ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Common error handling scenarios")]
        [Xunit.TraitAttribute("FeatureTitle", "Common Steps")]
        [Xunit.TraitAttribute("Description", "Common error handling scenarios")]
        [Xunit.TraitAttribute("Category", "shared-error-handling")]
        public async global::System.Threading.Tasks.Task CommonErrorHandlingScenarios()
        {
            string[] tagsOfScenario = new string[] {
                    "shared-error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Common error handling scenarios", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 62
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
                global::Reqnroll.Table table6 = new global::Reqnroll.Table(new string[] {
                            "Error Type",
                            "Error Message",
                            "Recovery Action"});
                table6.AddRow(new string[] {
                            "Network Unavailable",
                            "No internet connection",
                            "Retry or work offline"});
                table6.AddRow(new string[] {
                            "Storage Full",
                            "Insufficient storage space",
                            "Free space or skip"});
                table6.AddRow(new string[] {
                            "Invalid Input",
                            "Please enter a valid value",
                            "Correct input"});
                table6.AddRow(new string[] {
                            "Duplicate Entry",
                            "This item already exists",
                            "Use different name"});
                table6.AddRow(new string[] {
                            "Missing Required",
                            "This field is required",
                            "Provide value"});
#line 63
    await testRunner.GivenAsync("the following error conditions can occur:", ((string)(null)), table6, "Given ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await CommonStepsFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await CommonStepsFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
