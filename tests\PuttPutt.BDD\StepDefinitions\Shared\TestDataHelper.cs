using Reqnroll;
using PuttPutt.Core.Models;

namespace PuttPutt.BDD.StepDefinitions.Shared;

/// <summary>
/// Helper class for accessing and managing test data stored in ScenarioContext
/// </summary>
public static class TestDataHelper
{
    private const string TestPlayersKey = "TestPlayers";
    private const string PlayerKeyPrefix = "Player_";

    /// <summary>
    /// Gets all test players from the ScenarioContext
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <returns>List of test players, or empty list if none exist</returns>
    public static List<Player> GetTestPlayers(this ScenarioContext scenarioContext)
    {
        if (scenarioContext.TryGetValue(TestPlayersKey, out var players) && players is List<Player> playerList)
        {
            return playerList;
        }
        return new List<Player>();
    }

    /// <summary>
    /// Gets a specific test player by name from the ScenarioContext
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <param name="playerName">The name of the player to retrieve</param>
    /// <returns>The test player, or null if not found</returns>
    public static Player? GetTestPlayer(this ScenarioContext scenarioContext, string playerName)
    {
        var key = $"{PlayerKeyPrefix}{playerName}";
        if (scenarioContext.TryGetValue(key, out var player) && player is Player testPlayer)
        {
            return testPlayer;
        }
        return null;
    }

    /// <summary>
    /// Checks if a test player exists in the ScenarioContext
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <param name="playerName">The name of the player to check</param>
    /// <returns>True if the player exists, false otherwise</returns>
    public static bool HasTestPlayer(this ScenarioContext scenarioContext, string playerName)
    {
        return scenarioContext.GetTestPlayer(playerName) != null;
    }

    /// <summary>
    /// Gets all favorite test players from the ScenarioContext
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <returns>List of favorite test players</returns>
    public static List<Player> GetFavoriteTestPlayers(this ScenarioContext scenarioContext)
    {
        return scenarioContext.GetTestPlayers()
            .Where(p => p.IsFavorite)
            .ToList();
    }

    /// <summary>
    /// Gets test players by color from the ScenarioContext
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <param name="color">The color to filter by</param>
    /// <returns>List of test players with the specified color</returns>
    public static List<Player> GetTestPlayersByColor(this ScenarioContext scenarioContext, string color)
    {
        return scenarioContext.GetTestPlayers()
            .Where(p => string.Equals(p.Color, color, StringComparison.OrdinalIgnoreCase))
            .ToList();
    }

    /// <summary>
    /// Gets the count of test players in the ScenarioContext
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <returns>The number of test players</returns>
    public static int GetTestPlayerCount(this ScenarioContext scenarioContext)
    {
        return scenarioContext.GetTestPlayers().Count;
    }

    /// <summary>
    /// Validates that the expected players exist in the test data
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <param name="expectedPlayerNames">The names of players that should exist</param>
    /// <exception cref="InvalidOperationException">Thrown if any expected players are missing</exception>
    public static void ValidatePlayersExist(this ScenarioContext scenarioContext, params string[] expectedPlayerNames)
    {
        var missingPlayers = expectedPlayerNames
            .Where(name => !scenarioContext.HasTestPlayer(name))
            .ToList();

        if (missingPlayers.Count > 0)
        {
            throw new InvalidOperationException(
                $"The following expected players are missing from test data: {string.Join(", ", missingPlayers)}");
        }
    }

    /// <summary>
    /// Gets a formatted string representation of all test players for debugging
    /// </summary>
    /// <param name="scenarioContext">The scenario context</param>
    /// <returns>Formatted string with all player information</returns>
    public static string GetTestPlayersDebugInfo(this ScenarioContext scenarioContext)
    {
        var players = scenarioContext.GetTestPlayers();
        if (players.Count == 0)
        {
            return "No test players found in ScenarioContext";
        }

        var info = $"Test Players ({players.Count}):\n";
        foreach (var player in players)
        {
            info += $"  - {player}\n";
        }
        return info.TrimEnd();
    }
}
