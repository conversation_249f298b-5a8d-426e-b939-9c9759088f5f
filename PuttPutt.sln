﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PuttPutt.BDD", "tests\PuttPutt.BDD\PuttPutt.BDD.csproj", "{7034BB74-FA80-45ED-BD82-AC9678E482BE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		docs\AUDITTRAIL.md = docs\AUDITTRAIL.md
		docs\core-features.md = docs\core-features.md
		docs\general-course-rules.md = docs\general-course-rules.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PuttPutt.Core", "src\PuttPutt.Core\PuttPutt.Core.csproj", "{ACDBF0F9-3110-4E15-081A-82A5141721C3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{C03438C1-2519-404B-A550-5CEE76E14BC0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Debug|x64.Build.0 = Debug|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Debug|x86.Build.0 = Debug|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Release|x64.ActiveCfg = Release|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Release|x64.Build.0 = Release|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Release|x86.ActiveCfg = Release|Any CPU
		{7034BB74-FA80-45ED-BD82-AC9678E482BE}.Release|x86.Build.0 = Release|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Debug|x64.Build.0 = Debug|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Debug|x86.Build.0 = Debug|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Release|x64.ActiveCfg = Release|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Release|x64.Build.0 = Release|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Release|x86.ActiveCfg = Release|Any CPU
		{ACDBF0F9-3110-4E15-081A-82A5141721C3}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7034BB74-FA80-45ED-BD82-AC9678E482BE} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{ACDBF0F9-3110-4E15-081A-82A5141721C3} = {C03438C1-2519-404B-A550-5CEE76E14BC0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6A0C9281-F501-4A3F-8098-B5F993F1EFD8}
	EndGlobalSection
EndGlobal
