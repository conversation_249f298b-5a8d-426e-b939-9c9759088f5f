﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:2.0.0.0
//      Reqnroll Generator Version:2.0.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "game-setup")]
    public partial class GameSetupFeature : object, Xunit.IClassFixture<GameSetupFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "game-setup"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Game Setup", "  As a putt-putt player\r\n  I want to set up new games with selected players and c" +
                "ourses\r\n  So that I can start playing and tracking scores", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "GameSetup.feature"
#line hidden
        
        public GameSetupFeature(GameSetupFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("the app is launched", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("I have players \"John Doe\", \"Jane Smith\", and \"Bob Johnson\" in my player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create a new game with existing players")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Create a new game with existing players")]
        [Xunit.TraitAttribute("Category", "smoke")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task CreateANewGameWithExistingPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "smoke",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create a new game with existing players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 12
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 13
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 14
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 15
    await testRunner.AndAsync("I select players \"John Doe\" and \"Jane Smith\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 16
    await testRunner.AndAsync("I choose \"18 holes\" for the game", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 17
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
    await testRunner.ThenAsync("a new game should be created", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 19
    await testRunner.AndAsync("the game should include \"John Doe\" and \"Jane Smith\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 20
    await testRunner.AndAsync("the game should be set for 18 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 21
    await testRunner.AndAsync("I should be taken to the scoring screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create a game with guest players")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Create a game with guest players")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task CreateAGameWithGuestPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create a game with guest players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 24
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 25
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 26
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 27
    await testRunner.AndAsync("I select player \"John Doe\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 28
    await testRunner.AndAsync("I tap \"Add Guest Player\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 29
    await testRunner.AndAsync("I enter \"Guest Player 1\" as the guest name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 30
    await testRunner.AndAsync("I choose \"9 holes\" for the game", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 31
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 32
    await testRunner.ThenAsync("a new game should be created", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 33
    await testRunner.AndAsync("the game should include \"John Doe\" and \"Guest Player 1\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 34
    await testRunner.AndAsync("the guest player should not be saved to the player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 35
    await testRunner.AndAsync("the game should be set for 9 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create a game with custom number of holes")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Create a game with custom number of holes")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task CreateAGameWithCustomNumberOfHoles()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create a game with custom number of holes", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 38
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 39
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 40
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 41
    await testRunner.AndAsync("I select players \"Jane Smith\" and \"Bob Johnson\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 42
    await testRunner.AndAsync("I choose \"Custom\" for the number of holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 43
    await testRunner.AndAsync("I enter \"12\" as the custom hole count", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 44
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 45
    await testRunner.ThenAsync("a new game should be created", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 46
    await testRunner.AndAsync("the game should be set for 12 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create a game with a predefined course")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Create a game with a predefined course")]
        [Xunit.TraitAttribute("Category", "course-management")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task CreateAGameWithAPredefinedCourse()
        {
            string[] tagsOfScenario = new string[] {
                    "course-management",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create a game with a predefined course", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 49
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 50
    await testRunner.GivenAsync("I have a course \"Adventure Course\" with 18 holes and par values", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 51
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 52
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 53
    await testRunner.AndAsync("I select players \"John Doe\" and \"Jane Smith\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 54
    await testRunner.AndAsync("I select \"Adventure Course\" from the course list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 55
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 56
    await testRunner.ThenAsync("a new game should be created", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 57
    await testRunner.AndAsync("the game should use the \"Adventure Course\" settings", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 58
    await testRunner.AndAsync("each hole should have the predefined par values", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Set up game using QR code course configuration")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Set up game using QR code course configuration")]
        [Xunit.TraitAttribute("Category", "course-management")]
        [Xunit.TraitAttribute("Category", "qr-code")]
        public async global::System.Threading.Tasks.Task SetUpGameUsingQRCodeCourseConfiguration()
        {
            string[] tagsOfScenario = new string[] {
                    "course-management",
                    "qr-code"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Set up game using QR code course configuration", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 61
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 62
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 63
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 64
    await testRunner.AndAsync("I select players \"John Doe\" and \"Bob Johnson\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 65
    await testRunner.AndAsync("I tap \"Scan Course QR Code\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 66
    await testRunner.AndAsync("I scan a valid course QR code for \"Main Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 67
    await testRunner.ThenAsync("the course should be automatically configured", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 68
    await testRunner.AndAsync("the course name should be set to \"Main Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 69
    await testRunner.AndAsync("the hole count and par values should be loaded from the QR code", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 70
    await testRunner.AndAsync("I should be able to proceed with \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot start game without selecting players")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Cannot start game without selecting players")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotStartGameWithoutSelectingPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot start game without selecting players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 73
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 74
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 75
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 76
    await testRunner.AndAsync("I do not select any players", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 77
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 78
    await testRunner.ThenAsync("I should see an error message \"At least one player must be selected\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 79
    await testRunner.AndAsync("the game should not start", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot start game with invalid custom hole count")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Cannot start game with invalid custom hole count")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotStartGameWithInvalidCustomHoleCount()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot start game with invalid custom hole count", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 82
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 83
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 84
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 85
    await testRunner.AndAsync("I select player \"John Doe\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 86
    await testRunner.AndAsync("I choose \"Custom\" for the number of holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 87
    await testRunner.AndAsync("I enter \"0\" as the custom hole count", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 88
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 89
    await testRunner.ThenAsync("I should see an error message \"Number of holes must be between 1 and 50\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 90
    await testRunner.AndAsync("the game should not start", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create game with maximum number of players")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Create game with maximum number of players")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        public async global::System.Threading.Tasks.Task CreateGameWithMaximumNumberOfPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create game with maximum number of players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 93
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 94
    await testRunner.GivenAsync("I have 8 players in my player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 95
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 96
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 97
    await testRunner.AndAsync("I select all 8 players", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 98
    await testRunner.AndAsync("I choose \"18 holes\" for the game", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 99
    await testRunner.AndAsync("I tap \"Start Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 100
    await testRunner.ThenAsync("a new game should be created with all 8 players", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 101
    await testRunner.AndAsync("the scoring interface should accommodate all players", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot add more than maximum allowed players")]
        [Xunit.TraitAttribute("FeatureTitle", "Game Setup")]
        [Xunit.TraitAttribute("Description", "Cannot add more than maximum allowed players")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotAddMoreThanMaximumAllowedPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot add more than maximum allowed players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 104
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 105
    await testRunner.GivenAsync("I have 8 players in my player list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 106
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 107
    await testRunner.WhenAsync("I tap \"New Game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 108
    await testRunner.AndAsync("I select all 8 players", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 109
    await testRunner.AndAsync("I try to add a guest player", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 110
    await testRunner.ThenAsync("I should see a message \"Maximum of 8 players allowed per game\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 111
    await testRunner.AndAsync("the guest player option should be disabled", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await GameSetupFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await GameSetupFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
