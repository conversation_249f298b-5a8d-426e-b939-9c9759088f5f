# Reqnroll ScenarioContext API and Table Examples

In Reqnroll, `ScenarioContext` is a class that provides access to contextual information during a scenario's execution. It acts as a container for data that needs to be shared between steps within a scenario, or even between different hooks.

---

## Key API Members of `ScenarioContext`

Here are some of the most commonly used properties and methods of `ScenarioContext`:

* **`ScenarioContext.Current`**: (Static property) Provides access to the current `ScenarioContext` instance for the executing scenario.
* **`ScenarioContext.ScenarioInfo`**: (Property) Contains information about the current scenario, such as its title, tags, and arguments.
* **`ScenarioContext.FeatureContext`**: (Property) Provides access to the `FeatureContext` for the current feature.
* **`ScenarioContext.ScenarioContainer`**: (Property) The dependency injection container for the current scenario. You can resolve objects registered with the container.
* **`ScenarioContext.Get<T>(string key = null)`**: (Method) Retrieves a stored object of type `T` from the context. You can optionally specify a `key` if multiple objects of the same type are stored.
* **`ScenarioContext.Set<T>(T value, string key = null)`**: (Method) Stores an object of type `T` in the context. You can optionally specify a `key` to identify it.
* **`ScenarioContext.ContainsKey(string key)`**: (Method) Checks if a specific key exists in the context.
* **`ScenarioContext.Remove(string key)`**: (Method) Removes an object from the context by its key.

---

## Examples with Table Data

When working with table data in Reqnroll steps, the table is typically passed as a `Table` object to your step definition method. You can then transform this `Table` object into a collection of custom objects.

Let's assume you have the following feature file:

```gherkin
Feature: User Management

Scenario: Adding multiple users
    Given the following users exist:
        | Name  | Email             | Age |
        | John  | <EMAIL>  | 30  |
        | Jane  | <EMAIL>  | 25  |
        | Peter | <EMAIL> | 35  |
```

And you have a `User` class:

```csharp
public class User
{
    public string Name { get; set; }
    public string Email { get; set; }
    public int Age { get; set; }
}
```

Now, let's look at how you would get all entries vs. one entry from the table using `ScenarioContext` (though typically you'd process the table directly within the step definition as shown below).

### Getting All Entries (Converting Table to List of Objects)

You'd typically use the `Table.CreateSet<T>()` or `Table.CreateInstance<T>()` extension methods provided by Reqnroll to convert the table directly into a collection of objects. You could then store this collection in `ScenarioContext` if needed for later steps.

```csharp
using Reqnroll;
using Reqnroll.Assist; // Required for CreateSet and CreateInstance

[Binding]
public class UserSteps
{
    private readonly ScenarioContext _scenarioContext;

    public UserSteps(ScenarioContext scenarioContext)
    {
        _scenarioContext = scenarioContext;
    }

    [Given("the following users exist:")]
    public void GivenTheFollowingUsersExist(Table table)
    {
        // Get all entries from the table as a list of User objects
        var users = table.CreateSet<User>().ToList();

        // Store the list of users in ScenarioContext for later use
        _scenarioContext.Set(users, "UserList");

        // You can also iterate directly
        foreach (var user in users)
        {
            Console.WriteLine($"Adding user: {user.Name}, {user.Email}, {user.Age}");
            // Logic to add user to a system
        }
    }

    [Then("I can verify the users were added")]
    public void ThenICanVerifyTheUsersWereAdded()
    {
        // Retrieve the list of users from ScenarioContext
        var users = _scenarioContext.Get<List<User>>("UserList");

        foreach (var user in users)
        {
            Console.WriteLine($"Verifying user: {user.Name}");
            // Logic to verify user in the system
        }
    }
}
```

### Getting One Entry (Converting Table to a Single Object)

If your table represents a single entity, you can use `Table.CreateInstance<T>()`. While less common for tables with multiple rows, you might use it if your scenario design dictates a single configuration or specific data point per table.

```csharp
using Reqnroll;
using Reqnroll.Assist;

// Assume a scenario like:
// Scenario: Configure system with settings
//    Given the system has the following settings:
//        | Key      | Value      |
//        | Timeout  | 30         |
//        | Retries  | 5          |

public class SystemSettings
{
    public int Timeout { get; set; }
    public int Retries { get; set; }
}

[Binding]
public class SystemConfigurationSteps
{
    private readonly ScenarioContext _scenarioContext;

    public SystemConfigurationSteps(ScenarioContext scenarioContext)
    {
        _scenarioContext = scenarioContext;
    }

    [Given("the system has the following settings:")]
    public void GivenTheSystemHasTheFollowingSettings(Table table)
    {
        // Get a single instance from the table (assumes the table has one logical entry)
        var settings = table.CreateInstance<SystemSettings>();

        _scenarioContext.Set(settings, "SystemSettings");

        Console.WriteLine($"Configuring system with Timeout: {settings.Timeout}, Retries: {settings.Retries}");
    }

    [Then("the system settings should be applied")]
    public void ThenTheSystemSettingsShouldBeApplied()
    {
        var settings = _scenarioContext.Get<SystemSettings>("SystemSettings");
        Console.WriteLine($"Verifying system settings: Timeout={settings.Timeout}, Retries={settings.Retries}");
        // Assertions based on settings
    }
}
```

---

## Important Note on `ScenarioContext` Usage

While `ScenarioContext` is useful for sharing data between steps within the *same* scenario, it's generally good practice to:

* **Process the `Table` directly within the step definition** where it's passed, as shown in the examples. This keeps the data processing close to where it's received.
* **Use dependency injection for shared services or complex objects** rather than relying solely on `ScenarioContext` as a general-purpose data bag for everything. `ScenarioContext` is best for transient, scenario-specific data.
