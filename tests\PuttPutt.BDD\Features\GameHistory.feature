@game-history
Feature: Game History and Statistics
  As a putt-putt player
  I want to view my game history and statistics
  So that I can track my progress and review past performances

  Background:
    Given the app is launched
    And I have completed several games in the past

  @smoke @happy-path
  Scenario: View list of completed games
    Given I have completed games on different dates
    And I am on the main menu
    When I tap "Game History"
    Then I should see a list of all completed games
    And each game should show the date, course name, and players
    And the games should be sorted by date (most recent first)

  @happy-path
  Scenario: View detailed scorecard of a completed game
    Given I have a completed game from "2025-07-15" with players "<PERSON>" and "<PERSON>"
    And I am on the game history screen
    When I tap on the game from "2025-07-15"
    Then I should see the complete scorecard
    And I should see each player's score for every hole
    And I should see the final totals and rankings
    And I should see the course name and par values

  @save-load @happy-path
  Scenario: Save game progress automatically
    Given I have started a new game
    And I have entered scores for 5 holes
    When I close the app
    And I reopen the app
    Then I should see an option to "Resume Game"
    And the game should continue from hole 6
    And all previously entered scores should be preserved

  @save-load @happy-path
  Scenario: Resume an unfinished game
    Given I have an unfinished game saved
    And I am on the main menu
    When I tap "Resume Game"
    Then I should be taken to the scoring screen
    And I should be on the correct hole where I left off
    And all previous scores should be displayed correctly

  @save-load @happy-path
  Scenario: Save completed game manually
    Given I have just finished a game
    And I am on the final scorecard screen
    When I tap "Save Game"
    Then the game should be saved to game history
    And I should see a confirmation "Game saved successfully"
    And the game should appear in the game history list

  @statistics @happy-path
  Scenario: View player statistics overview
    Given I have completed multiple games as "John Doe"
    And I am on the main menu
    When I tap "Statistics"
    And I select "John Doe" from the player list
    Then I should see "John Doe"'s overall statistics
    And I should see the average score per game
    And I should see the best score achieved
    And I should see the total number of games played

  @statistics @happy-path
  Scenario: View hole-in-one achievements
    Given "Jane Smith" has achieved 3 hole-in-ones in past games
    And I am on the statistics screen
    When I select "Jane Smith" from the player list
    Then I should see "Hole-in-ones: 3" in the statistics
    And I should be able to tap to see details of each hole-in-one
    And each hole-in-one should show the date, course, and hole number

  @statistics @happy-path
  Scenario: View best scores by course
    Given "John Doe" has played "Adventure Course" multiple times
    And I am viewing "John Doe"'s statistics
    When I tap "Best Scores by Course"
    Then I should see the best score for "Adventure Course"
    And I should see the date when the best score was achieved
    And I should see scores for all courses played

  @statistics @happy-path
  Scenario: View scoring trends over time
    Given "Jane Smith" has played games over the past 6 months
    And I am viewing "Jane Smith"'s statistics
    When I tap "Scoring Trends"
    Then I should see a graph showing score improvements over time
    And I should see the average score per month
    And I should be able to see if performance is improving or declining

  @filtering @happy-path
  Scenario: Filter game history by date range
    Given I have games from the past year
    And I am on the game history screen
    When I tap "Filter"
    And I select "Last 30 days" as the date range
    Then I should only see games from the last 30 days
    And the filter should be clearly indicated at the top

  @filtering @happy-path
  Scenario: Filter game history by course
    Given I have played games on "Main Course" and "Adventure Course"
    And I am on the game history screen
    When I tap "Filter"
    And I select "Adventure Course" from the course filter
    Then I should only see games played on "Adventure Course"
    And the course filter should be clearly indicated

  @search @happy-path
  Scenario: Search game history by player name
    Given I have games with various players
    And I am on the game history screen
    When I tap the search icon
    And I enter "John" in the search field
    Then I should see only games that included a player named "John"
    And the search term should be highlighted in the results

  @data-persistence @happy-path
  Scenario: Maintain game history across app updates
    Given I have 20 completed games in my history
    When the app is updated to a new version
    And I open the updated app
    Then all 20 games should still be in my history
    And all game details should be preserved
    And statistics should be calculated correctly

  @error-handling
  Scenario: Handle corrupted game data gracefully
    Given I have a game with corrupted data in the history
    When I try to view that game's details
    Then I should see an error message "Unable to load game details"
    And the app should not crash
    And other games should still be accessible

  @edge-case
  Scenario: View statistics with no completed games
    Given I am a new user with no completed games
    When I tap "Statistics"
    Then I should see a message "No games completed yet"
    And I should see an option to "Start Your First Game"
    And no statistics should be displayed
