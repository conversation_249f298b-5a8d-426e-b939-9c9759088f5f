using Reqnroll;
using PuttPutt.Core.Models;

namespace PuttPutt.BDD.StepDefinitions.Shared;

[Binding]
public class SharedStepDefinitions
{
    private readonly ScenarioContext scenarioContext;

    public SharedStepDefinitions(ScenarioContext scenarioContext)
    {
        this.scenarioContext = scenarioContext;
    }

    [Given("I have the following players in my player list:")]
    public void GivenIHaveTheFollowingPlayersInMyPlayerList(Table table)
    {
        // Validate table structure
        var expectedColumns = new[] { "Name", "Color", "Favorite" };
        var actualColumns = table.Header.ToArray();

        if (!expectedColumns.SequenceEqual(actualColumns))
        {
            throw new ArgumentException(
                $"Expected table columns: [{string.Join(", ", expectedColumns)}], " +
                $"but got: [{string.Join(", ", actualColumns)}]");
        }

        // Parse table data into test player objects
        var players = new List<Player>();

        foreach (var row in table.Rows)
        {
            var name = row["Name"];
            var color = row["Color"];
            var favoriteText = row["Favorite"];

            // Validate required fields
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Player name cannot be empty");
            }

            if (string.IsNullOrWhiteSpace(color))
            {
                throw new ArgumentException($"Color cannot be empty for player '{name}'");
            }

            // Parse favorite field (Yes/No, True/False, 1/0)
            var isFavorite = ParseBooleanValue(favoriteText, $"Favorite value for player '{name}'");

            var player = new Player(name, color, isFavorite);
            players.Add(player);
        }

        // Validate no duplicate player names
        var duplicateNames = players
            .GroupBy(p => p.Name, StringComparer.OrdinalIgnoreCase)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key)
            .ToList();

        if (duplicateNames.Any())
        {
            throw new ArgumentException(
                $"Duplicate player names found: {string.Join(", ", duplicateNames)}");
        }

        // Store players in ScenarioContext for access by other step definitions
        scenarioContext["TestPlayers"] = players;

        // Also store individual players by name for easy lookup
        foreach (var player in players)
        {
            scenarioContext[$"Player_{player.Name}"] = player;
        }

        // Log the setup for debugging
        Console.WriteLine($"Set up {players.Count} test players:");
        foreach (var player in players)
        {
            Console.WriteLine($"  - {player}");
        }
    }

    [Given("I have the following courses available:")]
    public void GivenIHaveTheFollowingCoursesAvailable(Table table)
    {
        // Implementation for shared course setup
        throw new PendingStepException();
    }

    [Given("I have started a game with the following configuration:")]
    public void GivenIHaveStartedAGameWithTheFollowingConfiguration(Table table)
    {
        // Implementation for shared game setup
        throw new PendingStepException();
    }

    /// <summary>
    /// Helper method to parse boolean values from table data
    /// Supports: Yes/No, True/False, 1/0 (case-insensitive)
    /// </summary>
    private static bool ParseBooleanValue(string value, string fieldDescription)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            throw new ArgumentException($"{fieldDescription} cannot be empty");
        }

        var normalizedValue = value.Trim().ToLowerInvariant();

        return normalizedValue switch
        {
            "yes" or "true" or "1" => true,
            "no" or "false" or "0" => false,
            _ => throw new ArgumentException(
                $"Invalid boolean value '{value}' for {fieldDescription}. " +
                "Expected: Yes/No, True/False, or 1/0")
        };
    }
}