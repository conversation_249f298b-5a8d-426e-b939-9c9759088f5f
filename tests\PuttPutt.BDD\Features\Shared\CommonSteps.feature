@shared
Feature: Common Steps
  This feature file contains shared step definitions that are used across multiple feature files.
  These steps should not be executed as standalone scenarios but serve as reusable components.

  @shared-background
  Scenario: Common application setup steps
    Given the app is launched
    And the app has been initialized with default settings
    And the local database is available
    And I am on the main menu

  @shared-player-setup
  Scenario: Common player management steps
    Given I have the following players in my player list:
      | Name        | Color  | Favorite |
      | <PERSON>    | Blue   | Yes      |
      | <PERSON>  | Red    | No       |
      | <PERSON> | Green  | No       |
      | <PERSON> | Yellow | Yes      |

  @shared-course-setup
  Scenario: Common course setup steps
    Given I have the following courses available:
      | Course Name      | Holes | Par Values                          |
      | Main Course      |     9 |                   3,3,4,3,4,3,3,4,3 |
      | Adventure Course |    18 | 3,3,4,3,4,3,3,4,3,4,3,3,4,3,4,3,3,4 |
      | Mini Course      |     6 |                         3,3,3,3,3,3 |

  @shared-game-setup
  Scenario: Common game setup steps
    Given I have started a game with the following configuration:
      | Players        | <PERSON>, <PERSON> |
      | Course         | Main Course          |
      | Holes          |                    9 |
      | Current Hole   |                    1 |
      | Current Player | <PERSON>             |

  @shared-navigation
  Scenario: Common navigation steps
    Given I can navigate to the following screens:
      | Screen Name       | Navigation Path            |
      | Main Menu         | App Launch                 |
      | Player Management | Main Menu > Manage Players |
      | Course Management | Main Menu > Manage Courses |
      | Game History      | Main Menu > Game History   |
      | Statistics        | Main Menu > Statistics     |
      | Game Rules        | Main Menu > Game Rules     |
      | New Game Setup    | Main Menu > New Game       |

  @shared-validation
  Scenario: Common validation scenarios
    Given the following validation rules apply:
      | Field Type  | Min Length | Max Length | Required | Special Rules |
      | Player Name |          1 |         50 | Yes      | No duplicates |
      | Course Name |          1 |        100 | Yes      | No duplicates |
      | Hole Count  |          1 |         50 | Yes      | Numeric only  |
      | Score       |          1 |         10 | Yes      | Numeric only  |
      | Par Value   |          1 |          8 | Yes      | Numeric only  |

  @shared-error-handling
  Scenario: Common error handling scenarios
    Given the following error conditions can occur:
      | Error Type          | Error Message              | Recovery Action       |
      | Network Unavailable | No internet connection     | Retry or work offline |
      | Storage Full        | Insufficient storage space | Free space or skip    |
      | Invalid Input       | Please enter a valid value | Correct input         |
      | Duplicate Entry     | This item already exists   | Use different name    |
      | Missing Required    | This field is required     | Provide value         |
