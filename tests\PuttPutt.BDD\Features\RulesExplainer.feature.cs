﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:2.0.0.0
//      Reqnroll Generator Version:2.0.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "rules-explainer")]
    public partial class Mini_WikiRuleExplainerFeature : object, Xunit.IClassFixture<Mini_WikiRuleExplainerFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "rules-explainer"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Mini-wiki Rule Explainer", "  As a putt-putt player\r\n  I want to access game rules and explanations\r\n  So tha" +
                "t I can understand how to play and resolve any disputes", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "RulesExplainer.feature"
#line hidden
        
        public Mini_WikiRuleExplainerFeature(Mini_WikiRuleExplainerFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("the app is launched", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Access general game rules")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Access general game rules")]
        [Xunit.TraitAttribute("Category", "smoke")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task AccessGeneralGameRules()
        {
            string[] tagsOfScenario = new string[] {
                    "smoke",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Access general game rules", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 12
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 13
    await testRunner.GivenAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 14
    await testRunner.WhenAsync("I tap \"Game Rules\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 15
    await testRunner.ThenAsync("I should see the rules menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 16
    await testRunner.AndAsync("I should see an option for \"General Course Rules\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 17
    await testRunner.WhenAsync("I tap \"General Course Rules\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 18
    await testRunner.ThenAsync("I should see the general putt-putt rules displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 19
    await testRunner.AndAsync("the rules should be clearly formatted and easy to read", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Navigate through different rule sections")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Navigate through different rule sections")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task NavigateThroughDifferentRuleSections()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Navigate through different rule sections", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 22
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 23
    await testRunner.GivenAsync("I am viewing the game rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 24
    await testRunner.WhenAsync("I see the table of contents", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 25
    await testRunner.ThenAsync("I should see sections like \"Basic Gameplay\", \"Scoring\", \"Etiquette\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 26
    await testRunner.WhenAsync("I tap on \"Scoring\" section", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 27
    await testRunner.ThenAsync("I should be taken to the scoring rules section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 28
    await testRunner.AndAsync("I should see detailed information about how scoring works", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Search for specific rules")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Search for specific rules")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task SearchForSpecificRules()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Search for specific rules", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 31
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 32
    await testRunner.GivenAsync("I am viewing the game rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 33
    await testRunner.WhenAsync("I tap the search icon", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 34
    await testRunner.AndAsync("I enter \"maximum strokes\" in the search field", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 35
    await testRunner.ThenAsync("I should see search results related to stroke limits", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 36
    await testRunner.AndAsync("the relevant text should be highlighted", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 37
    await testRunner.AndAsync("I should be able to tap a result to jump to that section", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View rules while in an active game")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "View rules while in an active game")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewRulesWhileInAnActiveGame()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View rules while in an active game", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 40
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 41
    await testRunner.GivenAsync("I have an active game in progress", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 42
    await testRunner.AndAsync("I am on the scoring screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 43
    await testRunner.WhenAsync("I tap the menu button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 44
    await testRunner.AndAsync("I select \"View Rules\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 45
    await testRunner.ThenAsync("the rules should open in an overlay or new screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 46
    await testRunner.AndAsync("I should be able to return to my game easily", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 47
    await testRunner.AndAsync("my game progress should be preserved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Access course-specific rules")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Access course-specific rules")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task AccessCourse_SpecificRules()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Access course-specific rules", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 50
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 51
    await testRunner.GivenAsync("I have selected a specific course for my game", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 52
    await testRunner.AndAsync("the course has custom rules or notes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 53
    await testRunner.WhenAsync("I tap \"Course Rules\" during game setup", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 54
    await testRunner.ThenAsync("I should see rules specific to the selected course", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 55
    await testRunner.AndAsync("I should see any special hole instructions or hazards", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 56
    await testRunner.AndAsync("I should see how these rules differ from general rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Bookmark frequently referenced rules")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Bookmark frequently referenced rules")]
        [Xunit.TraitAttribute("Category", "bookmark")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task BookmarkFrequentlyReferencedRules()
        {
            string[] tagsOfScenario = new string[] {
                    "bookmark",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Bookmark frequently referenced rules", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 59
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 60
    await testRunner.GivenAsync("I am viewing a specific rule section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 61
    await testRunner.WhenAsync("I tap the bookmark icon", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 62
    await testRunner.ThenAsync("the section should be added to my bookmarks", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 63
    await testRunner.AndAsync("I should see a confirmation \"Rule bookmarked\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 64
    await testRunner.WhenAsync("I go to \"Bookmarked Rules\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 65
    await testRunner.ThenAsync("I should see the bookmarked section in my list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Access rules without internet connection")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Access rules without internet connection")]
        [Xunit.TraitAttribute("Category", "offline")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task AccessRulesWithoutInternetConnection()
        {
            string[] tagsOfScenario = new string[] {
                    "offline",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Access rules without internet connection", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 68
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 69
    await testRunner.GivenAsync("I have no internet connection", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 70
    await testRunner.WhenAsync("I tap \"Game Rules\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 71
    await testRunner.ThenAsync("I should still be able to access all general rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 72
    await testRunner.AndAsync("all text and images should load properly", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 73
    await testRunner.AndAsync("the search functionality should work offline", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View rules with visual examples")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "View rules with visual examples")]
        [Xunit.TraitAttribute("Category", "visual-aids")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewRulesWithVisualExamples()
        {
            string[] tagsOfScenario = new string[] {
                    "visual-aids",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View rules with visual examples", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 76
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 77
    await testRunner.GivenAsync("I am viewing the rules section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 78
    await testRunner.WhenAsync("I navigate to \"Common Situations\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 79
    await testRunner.ThenAsync("I should see diagrams or illustrations", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 80
    await testRunner.AndAsync("each visual should have a clear caption", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 81
    await testRunner.AndAsync("the visuals should help explain complex rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Share specific rule with other players")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Share specific rule with other players")]
        [Xunit.TraitAttribute("Category", "sharing")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ShareSpecificRuleWithOtherPlayers()
        {
            string[] tagsOfScenario = new string[] {
                    "sharing",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Share specific rule with other players", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 84
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 85
    await testRunner.GivenAsync("I am viewing a specific rule section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 86
    await testRunner.WhenAsync("I tap the share button", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 87
    await testRunner.ThenAsync("I should be able to share the rule text", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 88
    await testRunner.AndAsync("I should be able to send it via messaging or email", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 89
    await testRunner.AndAsync("the shared content should include the rule title and text", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Provide feedback on rule clarity")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Provide feedback on rule clarity")]
        [Xunit.TraitAttribute("Category", "feedback")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ProvideFeedbackOnRuleClarity()
        {
            string[] tagsOfScenario = new string[] {
                    "feedback",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Provide feedback on rule clarity", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 92
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 93
    await testRunner.GivenAsync("I am viewing a specific rule", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 94
    await testRunner.WhenAsync("I scroll to the bottom of the rule section", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 95
    await testRunner.ThenAsync("I should see a \"Was this helpful?\" section", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 96
    await testRunner.AndAsync("I should be able to rate the rule explanation", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 97
    await testRunner.AndAsync("I should be able to provide optional feedback comments", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Receive rule updates when available")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Receive rule updates when available")]
        [Xunit.TraitAttribute("Category", "updates")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ReceiveRuleUpdatesWhenAvailable()
        {
            string[] tagsOfScenario = new string[] {
                    "updates",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Receive rule updates when available", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 100
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 101
    await testRunner.GivenAsync("I have internet connection", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 102
    await testRunner.AndAsync("there are updated rules available", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 103
    await testRunner.WhenAsync("I open the rules section", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 104
    await testRunner.ThenAsync("I should see a notification about rule updates", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 105
    await testRunner.AndAsync("I should be able to download the updated rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 106
    await testRunner.AndAsync("I should see what has changed in the updates", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Rules are accessible to all users")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Rules are accessible to all users")]
        [Xunit.TraitAttribute("Category", "accessibility")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task RulesAreAccessibleToAllUsers()
        {
            string[] tagsOfScenario = new string[] {
                    "accessibility",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Rules are accessible to all users", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 109
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 110
    await testRunner.GivenAsync("I have accessibility features enabled", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 111
    await testRunner.WhenAsync("I view the game rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 112
    await testRunner.ThenAsync("all text should be readable by screen readers", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 113
    await testRunner.AndAsync("I should be able to adjust text size", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 114
    await testRunner.AndAsync("high contrast mode should be supported", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 115
    await testRunner.AndAsync("images should have descriptive alt text", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Access quick reference during disputes")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Access quick reference during disputes")]
        [Xunit.TraitAttribute("Category", "quick-reference")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task AccessQuickReferenceDuringDisputes()
        {
            string[] tagsOfScenario = new string[] {
                    "quick-reference",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Access quick reference during disputes", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 118
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 119
    await testRunner.GivenAsync("I am in an active game", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 120
    await testRunner.AndAsync("there is a question about a rule", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 121
    await testRunner.WhenAsync("I tap \"Quick Reference\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 122
    await testRunner.ThenAsync("I should see a condensed version of key rules", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 123
    await testRunner.AndAsync("I should see common situations and their resolutions", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 124
    await testRunner.AndAsync("I should be able to find answers quickly without extensive reading", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="View recently accessed rules")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "View recently accessed rules")]
        [Xunit.TraitAttribute("Category", "history")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ViewRecentlyAccessedRules()
        {
            string[] tagsOfScenario = new string[] {
                    "history",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View recently accessed rules", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 127
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 128
    await testRunner.GivenAsync("I have viewed several rule sections", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 129
    await testRunner.WhenAsync("I tap \"Recently Viewed\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 130
    await testRunner.ThenAsync("I should see a list of the last 5 rule sections I accessed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 131
    await testRunner.AndAsync("I should be able to quickly return to any of them", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 132
    await testRunner.AndAsync("the list should be ordered by most recent first", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Handle missing rule content gracefully")]
        [Xunit.TraitAttribute("FeatureTitle", "Mini-wiki Rule Explainer")]
        [Xunit.TraitAttribute("Description", "Handle missing rule content gracefully")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task HandleMissingRuleContentGracefully()
        {
            string[] tagsOfScenario = new string[] {
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Handle missing rule content gracefully", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 135
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 136
    await testRunner.GivenAsync("a specific rule section fails to load", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 137
    await testRunner.WhenAsync("I try to access that section", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 138
    await testRunner.ThenAsync("I should see an error message \"Rule content unavailable\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 139
    await testRunner.AndAsync("I should see an option to \"Try Again\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 140
    await testRunner.AndAsync("I should be able to access other rule sections normally", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await Mini_WikiRuleExplainerFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await Mini_WikiRuleExplainerFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
