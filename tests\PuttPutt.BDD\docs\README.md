# Putt-Putt Mobile App - BDD Feature Tests

This directory contains Behavior-Driven Development (BDD) feature files written in Gherkin syntax for the Putt-Putt Mobile App. These feature files serve as both documentation and the foundation for automated acceptance testing.

## Overview

The BDD tests are organized around the core features identified in `docs/core-features.md`:

1. **Player Management** - Managing player profiles and preferences
2. **Game Setup** - Creating new games with players and courses
3. **Live Scoring** - Real-time score entry and tracking during games
4. **Course Management** - Managing different putt-putt courses and configurations
5. **Game History** - Viewing past games and player statistics
6. **User Experience** - Ensuring the app is intuitive and reliable
7. **Rules Explainer** - Accessing game rules and explanations

## Feature File Structure

```
Features/
├── Shared/
│   └── CommonSteps.feature          # Reusable step definitions and common scenarios
├── PlayerManagement.feature         # Player profile management scenarios
├── GameSetup.feature               # New game creation and configuration
├── LiveScoring.feature             # Real-time scoring during games
├── CourseManagement.feature        # Course creation and management
├── GameHistory.feature             # Game history and statistics
├── UserExperience.feature          # UI/UX and cross-platform scenarios
└── RulesExplainer.feature          # Game rules and help system
```

## Tag Organization

The feature files use tags for test organization and execution filtering:

### Feature-Level Tags
- `@player-management` - Player profile management tests
- `@game-setup` - Game creation and setup tests
- `@live-scoring` - Real-time scoring tests
- `@course-management` - Course management tests
- `@game-history` - History and statistics tests
- `@user-experience` - UX and usability tests
- `@rules-explainer` - Rules and help system tests

### Scenario-Level Tags
- `@smoke` - Critical functionality that must work
- `@happy-path` - Normal user workflows
- `@error-handling` - Error conditions and validation
- `@edge-case` - Boundary conditions and unusual scenarios
- `@validation` - Input validation scenarios
- `@offline` - Offline functionality tests
- `@qr-code` - QR code scanning features
- `@location-based` - Location-aware features
- `@cross-platform` - Platform-specific behavior
- `@accessibility` - Accessibility compliance
- `@performance` - Performance-related scenarios

### Functional Tags
- `@undo` - Undo functionality
- `@share` - Sharing capabilities
- `@save-load` - Save and load operations
- `@statistics` - Statistical calculations
- `@visual-feedback` - UI feedback and animations
- `@responsive-ui` - Responsive design
- `@data-persistence` - Data storage and retrieval

## Running Tests

### Run All Tests
```bash
dotnet test
```

### Run Tests by Tag
```bash
# Run only smoke tests
dotnet test --filter "Category=smoke"

# Run player management tests
dotnet test --filter "Category=player-management"

# Run happy path scenarios only
dotnet test --filter "Category=happy-path"

# Run error handling tests
dotnet test --filter "Category=error-handling"
```

### Run Tests by Feature
```bash
# Run specific feature file
dotnet test --filter "FullyQualifiedName~PlayerManagement"
```

## Scenario Structure

Each scenario follows the Given-When-Then pattern:

- **Given** - Sets up the initial context and preconditions
- **When** - Describes the action or event that triggers the behavior
- **Then** - Specifies the expected outcome or result

### Example Scenario
```gherkin
@smoke @happy-path
Scenario: Add a new player profile
  Given I am on the player management screen
  When I tap the "Add Player" button
  And I enter "John Doe" as the player name
  And I select a blue color for the player
  And I tap "Save Player"
  Then the player "John Doe" should be added to the player list
  And the player should have a blue color indicator
```

## Background Steps

Many feature files use `Background` sections to set up common preconditions that apply to all scenarios in that feature. This reduces duplication and makes scenarios more focused.

## Shared Steps

The `Features/Shared/CommonSteps.feature` file contains reusable step definitions and common test data that can be referenced across multiple feature files. This promotes consistency and reduces maintenance overhead.

## Test Data

Test scenarios use realistic data that reflects actual usage patterns:
- Player names like "John Doe", "Jane Smith"
- Course names like "Main Course", "Adventure Course"
- Typical hole counts (9, 18) and par values
- Common score ranges and game situations

## Implementation Guidelines

When implementing step definitions:

1. **Keep steps atomic** - Each step should test one specific behavior
2. **Use descriptive assertions** - Make it clear what is being verified
3. **Handle async operations** - Many mobile operations are asynchronous
4. **Support multiple platforms** - Tests should work on both Android and iOS
5. **Include accessibility** - Verify screen reader compatibility
6. **Test offline scenarios** - Ensure functionality works without internet

## Coverage Areas

The BDD tests cover:

### Functional Requirements
- ✅ Player profile management (CRUD operations)
- ✅ Game setup and configuration
- ✅ Real-time score entry and tracking
- ✅ Course management and QR code scanning
- ✅ Game history and statistics
- ✅ Rules and help system

### Non-Functional Requirements
- ✅ Offline functionality
- ✅ Cross-platform compatibility (Android/iOS)
- ✅ Responsive UI design
- ✅ Data persistence
- ✅ Performance and battery optimization
- ✅ Accessibility compliance
- ✅ Error handling and recovery

### User Experience
- ✅ Intuitive navigation
- ✅ Visual feedback and animations
- ✅ Undo functionality
- ✅ Sharing capabilities
- ✅ Search and filtering

## Next Steps

1. **Set up SpecFlow** - Add SpecFlow NuGet packages to enable Gherkin parsing
2. **Implement step definitions** - Create C# step definition classes
3. **Set up test infrastructure** - Configure test runners and reporting
4. **Add UI automation** - Integrate with Appium or similar for mobile testing
5. **Continuous integration** - Add BDD tests to CI/CD pipeline

## Contributing

When adding new scenarios:
1. Follow the existing naming conventions
2. Use appropriate tags for organization
3. Include both positive and negative test cases
4. Add edge cases and error conditions
5. Update this README if adding new features or tags
