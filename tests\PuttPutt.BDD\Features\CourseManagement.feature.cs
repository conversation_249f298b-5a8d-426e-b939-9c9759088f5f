﻿// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:2.0.0.0
//      Reqnroll Generator Version:2.0.0.0
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace PuttPutt.BDD.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [Xunit.TraitAttribute("Category", "course-management")]
    public partial class CourseManagementFeature : object, Xunit.IClassFixture<CourseManagementFeature.FixtureData>, Xunit.IAsyncLifetime
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "course-management"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Course Management", "  As a putt-putt facility manager or player\r\n  I want to manage different courses" +
                " and their configurations\r\n  So that games can be played with accurate par value" +
                "s and course-specific settings", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
        private Xunit.Abstractions.ITestOutputHelper _testOutputHelper;
        
#line 1 "CourseManagement.feature"
#line hidden
        
        public CourseManagementFeature(CourseManagementFeature.FixtureData fixtureData, Xunit.Abstractions.ITestOutputHelper testOutputHelper)
        {
            this._testOutputHelper = testOutputHelper;
        }
        
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<Xunit.Abstractions.ITestOutputHelper>(_testOutputHelper);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 7
  #line hidden
#line 8
    await testRunner.GivenAsync("the app is launched", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 9
    await testRunner.AndAsync("I am on the main menu", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
        {
            try
            {
                await this.TestInitializeAsync();
            }
            catch (System.Exception e1)
            {
                try
                {
                    ((Xunit.IAsyncLifetime)(this)).DisposeAsync();
                }
                catch (System.Exception e2)
                {
                    throw new System.AggregateException("Test initialization failed", e1, e2);
                }
                throw;
            }
        }
        
        async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
        {
            await this.TestTearDownAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create a new course")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Create a new course")]
        [Xunit.TraitAttribute("Category", "smoke")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task CreateANewCourse()
        {
            string[] tagsOfScenario = new string[] {
                    "smoke",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create a new course", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 12
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 13
    await testRunner.GivenAsync("I navigate to the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 14
    await testRunner.WhenAsync("I tap \"Add New Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 15
    await testRunner.AndAsync("I enter \"Adventure Course\" as the course name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 16
    await testRunner.AndAsync("I set the number of holes to 18", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 17
    await testRunner.AndAsync("I set par values for all 18 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
    await testRunner.AndAsync("I tap \"Save Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 19
    await testRunner.ThenAsync("the course \"Adventure Course\" should be added to the course list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 20
    await testRunner.AndAsync("the course should have 18 holes with the specified par values", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Edit an existing course")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Edit an existing course")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task EditAnExistingCourse()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Edit an existing course", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 23
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 24
    await testRunner.GivenAsync("I have a course \"Main Course\" with 9 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 25
    await testRunner.AndAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 26
    await testRunner.WhenAsync("I tap on \"Main Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 27
    await testRunner.AndAsync("I change the course name to \"Classic Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 28
    await testRunner.AndAsync("I modify the par value for hole 3 from 3 to 4", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 29
    await testRunner.AndAsync("I tap \"Save Changes\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 30
    await testRunner.ThenAsync("the course should be updated to \"Classic Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 31
    await testRunner.AndAsync("hole 3 should have a par value of 4", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Delete a course")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Delete a course")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task DeleteACourse()
        {
            string[] tagsOfScenario = new string[] {
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Delete a course", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 34
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 35
    await testRunner.GivenAsync("I have a course \"Old Course\" in the course list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 36
    await testRunner.AndAsync("the course is not currently being used in any active games", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 37
    await testRunner.AndAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 38
    await testRunner.WhenAsync("I tap on \"Old Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 39
    await testRunner.AndAsync("I tap \"Delete Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 40
    await testRunner.AndAsync("I confirm the deletion", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 41
    await testRunner.ThenAsync("\"Old Course\" should be removed from the course list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Generate QR code for course configuration")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Generate QR code for course configuration")]
        [Xunit.TraitAttribute("Category", "qr-code")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task GenerateQRCodeForCourseConfiguration()
        {
            string[] tagsOfScenario = new string[] {
                    "qr-code",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Generate QR code for course configuration", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 44
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 45
    await testRunner.GivenAsync("I have a course \"Adventure Course\" with complete configuration", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 46
    await testRunner.AndAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 47
    await testRunner.WhenAsync("I tap on \"Adventure Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 48
    await testRunner.AndAsync("I tap \"Generate QR Code\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 49
    await testRunner.ThenAsync("a QR code should be generated containing the course configuration", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 50
    await testRunner.AndAsync("I should be able to share or save the QR code", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 51
    await testRunner.AndAsync("the QR code should include course name, hole count, and par values", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Import course configuration from QR code")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Import course configuration from QR code")]
        [Xunit.TraitAttribute("Category", "qr-code")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task ImportCourseConfigurationFromQRCode()
        {
            string[] tagsOfScenario = new string[] {
                    "qr-code",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Import course configuration from QR code", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 54
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 55
    await testRunner.GivenAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 56
    await testRunner.WhenAsync("I tap \"Import from QR Code\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 57
    await testRunner.AndAsync("I scan a valid course QR code", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 58
    await testRunner.ThenAsync("the course configuration should be imported", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 59
    await testRunner.AndAsync("I should see a preview of the course details", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 60
    await testRunner.AndAsync("I should be able to save the imported course", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Download course based on location proximity")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Download course based on location proximity")]
        [Xunit.TraitAttribute("Category", "location-based")]
        [Xunit.TraitAttribute("Category", "happy-path")]
        public async global::System.Threading.Tasks.Task DownloadCourseBasedOnLocationProximity()
        {
            string[] tagsOfScenario = new string[] {
                    "location-based",
                    "happy-path"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Download course based on location proximity", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 63
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 64
    await testRunner.GivenAsync("location services are enabled", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 65
    await testRunner.AndAsync("I am within 100 meters of \"Sunny Valley Putt-Putt\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 66
    await testRunner.WhenAsync("I navigate to the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 67
    await testRunner.ThenAsync("I should see a notification \"Course available for download\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 68
    await testRunner.WhenAsync("I tap \"Download Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 69
    await testRunner.ThenAsync("the \"Sunny Valley Putt-Putt\" course should be downloaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 70
    await testRunner.AndAsync("the course should appear in my course list", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot create course with empty name")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Cannot create course with empty name")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotCreateCourseWithEmptyName()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot create course with empty name", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 73
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 74
    await testRunner.GivenAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 75
    await testRunner.WhenAsync("I tap \"Add New Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 76
    await testRunner.AndAsync("I leave the course name field empty", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 77
    await testRunner.AndAsync("I set the number of holes to 9", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 78
    await testRunner.AndAsync("I tap \"Save Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 79
    await testRunner.ThenAsync("I should see an error message \"Course name is required\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 80
    await testRunner.AndAsync("the course should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot create course with invalid hole count")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Cannot create course with invalid hole count")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotCreateCourseWithInvalidHoleCount()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot create course with invalid hole count", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 83
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 84
    await testRunner.GivenAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 85
    await testRunner.WhenAsync("I tap \"Add New Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 86
    await testRunner.AndAsync("I enter \"Test Course\" as the course name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 87
    await testRunner.AndAsync("I set the number of holes to 0", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 88
    await testRunner.AndAsync("I tap \"Save Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 89
    await testRunner.ThenAsync("I should see an error message \"Number of holes must be between 1 and 50\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 90
    await testRunner.AndAsync("the course should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot delete course that is in use")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Cannot delete course that is in use")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotDeleteCourseThatIsInUse()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot delete course that is in use", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 93
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 94
    await testRunner.GivenAsync("I have a course \"Active Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 95
    await testRunner.AndAsync("there is an active game using \"Active Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 96
    await testRunner.AndAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 97
    await testRunner.WhenAsync("I tap on \"Active Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 98
    await testRunner.AndAsync("I tap \"Delete Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 99
    await testRunner.ThenAsync("I should see an error message \"Cannot delete course that is currently in use\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 100
    await testRunner.AndAsync("the course should not be deleted", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Cannot create duplicate course names")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Cannot create duplicate course names")]
        [Xunit.TraitAttribute("Category", "validation")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task CannotCreateDuplicateCourseNames()
        {
            string[] tagsOfScenario = new string[] {
                    "validation",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cannot create duplicate course names", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 103
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 104
    await testRunner.GivenAsync("I have a course \"Main Course\" in the course list", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 105
    await testRunner.AndAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 106
    await testRunner.WhenAsync("I tap \"Add New Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 107
    await testRunner.AndAsync("I enter \"Main Course\" as the course name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 108
    await testRunner.AndAsync("I set the number of holes to 18", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 109
    await testRunner.AndAsync("I tap \"Save Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 110
    await testRunner.ThenAsync("I should see an error message \"A course with this name already exists\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 111
    await testRunner.AndAsync("the duplicate course should not be saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Handle invalid QR code during import")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Handle invalid QR code during import")]
        [Xunit.TraitAttribute("Category", "qr-code")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task HandleInvalidQRCodeDuringImport()
        {
            string[] tagsOfScenario = new string[] {
                    "qr-code",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Handle invalid QR code during import", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 114
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 115
    await testRunner.GivenAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 116
    await testRunner.WhenAsync("I tap \"Import from QR Code\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 117
    await testRunner.AndAsync("I scan an invalid or corrupted QR code", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 118
    await testRunner.ThenAsync("I should see an error message \"Invalid course QR code\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 119
    await testRunner.AndAsync("no course should be imported", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Handle location services disabled")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Handle location services disabled")]
        [Xunit.TraitAttribute("Category", "location-based")]
        [Xunit.TraitAttribute("Category", "error-handling")]
        public async global::System.Threading.Tasks.Task HandleLocationServicesDisabled()
        {
            string[] tagsOfScenario = new string[] {
                    "location-based",
                    "error-handling"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Handle location services disabled", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 122
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 123
    await testRunner.GivenAsync("location services are disabled", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 124
    await testRunner.WhenAsync("I navigate to the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 125
    await testRunner.ThenAsync("I should not see any location-based course suggestions", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 126
    await testRunner.AndAsync("I should see a message \"Enable location services to discover nearby courses\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [Xunit.SkippableFactAttribute(DisplayName="Create course with maximum number of holes")]
        [Xunit.TraitAttribute("FeatureTitle", "Course Management")]
        [Xunit.TraitAttribute("Description", "Create course with maximum number of holes")]
        [Xunit.TraitAttribute("Category", "edge-case")]
        public async global::System.Threading.Tasks.Task CreateCourseWithMaximumNumberOfHoles()
        {
            string[] tagsOfScenario = new string[] {
                    "edge-case"};
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Create course with maximum number of holes", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 129
  this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 7
  await this.FeatureBackgroundAsync();
#line hidden
#line 130
    await testRunner.GivenAsync("I am on the course management screen", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 131
    await testRunner.WhenAsync("I tap \"Add New Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 132
    await testRunner.AndAsync("I enter \"Marathon Course\" as the course name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 133
    await testRunner.AndAsync("I set the number of holes to 50", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 134
    await testRunner.AndAsync("I set par values for all 50 holes", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 135
    await testRunner.AndAsync("I tap \"Save Course\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 136
    await testRunner.ThenAsync("the course should be created successfully", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 137
    await testRunner.AndAsync("all 50 holes should have their par values saved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "2.0.0.0")]
        [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
        public class FixtureData : object, Xunit.IAsyncLifetime
        {
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.InitializeAsync()
            {
                await CourseManagementFeature.FeatureSetupAsync();
            }
            
            async global::System.Threading.Tasks.Task Xunit.IAsyncLifetime.DisposeAsync()
            {
                await CourseManagementFeature.FeatureTearDownAsync();
            }
        }
    }
}
#pragma warning restore
#endregion
