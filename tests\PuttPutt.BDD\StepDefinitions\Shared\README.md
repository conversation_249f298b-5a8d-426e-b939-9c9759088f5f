# Shared Step Definitions - Player Test Data

This document explains how to use the shared player test data implementation in BDD scenarios.

## Overview

The `SharedStepDefinitions` class provides a robust implementation for setting up test player data that can be shared across multiple step definitions within the same scenario. This uses the `Player` class from the Core project (`PuttPutt.Core.Models.Player`) and follows BDD best practices by:

- Using Reqnroll's `ScenarioContext` for data sharing
- Providing proper dependency injection patterns
- Including comprehensive validation and error handling
- Supporting multiple boolean value formats
- Offering helper methods for easy data access

## Usage

### Basic Table Setup

Use the following step in your feature files to set up test players:

```gherkin
Given I have the following players in my player list:
  | Name        | Color  | Favorite |
  | <PERSON>    | Blue   | Yes      |
  | <PERSON>  | Red    | No       |
  | <PERSON> | Green  | No       |
  | <PERSON> | Yellow | Yes      |
```

### Supported Boolean Formats

The `Favorite` column supports multiple formats (case-insensitive):

- `Yes` / `No`
- `True` / `False`
- `1` / `0`

### Accessing Test Data in Step Definitions

#### 1. Constructor Injection

Your step definition classes should inject `ScenarioContext`:

```csharp
[Binding]
public class MyStepDefinitions
{
    private readonly ScenarioContext scenarioContext;

    public MyStepDefinitions(ScenarioContext scenarioContext)
    {
        this.scenarioContext = scenarioContext;
    }
}
```

#### 2. Using Helper Methods

The `TestDataHelper` class provides extension methods for easy data access:

```csharp
// Get all test players (returns List<Player>)
var allPlayers = scenarioContext.GetTestPlayers();

// Get a specific player by name
var player = scenarioContext.GetTestPlayer("John Doe");

// Check if a player exists
if (scenarioContext.HasTestPlayer("Jane Smith"))
{
    // Player exists
}

// Get only favorite players
var favoriteTestPlayers = scenarioContext.GetFavoriteTestPlayers();

// Get players by color
var bluePlayers = scenarioContext.GetTestPlayersByColor("Blue");

// Get player count
var count = scenarioContext.GetTestPlayerCount();

// Validate expected players exist
scenarioContext.ValidatePlayersExist("John Doe", "Jane Smith");
```

### Example Step Definitions

```csharp
[Then("I should see all players from my player list")]
public void ThenIShouldSeeAllPlayersFromMyPlayerList()
{
    var testPlayers = scenarioContext.GetTestPlayers();
    Assert.True(testPlayers.Count > 0, "Expected to have test players available");
    
    foreach (var player in testPlayers)
    {
        // Verify each player appears in the UI
        VerifyPlayerDisplayed(player.Name, player.Color, player.IsFavorite);
    }
}

[Then("favorite players should appear at the top of the list")]
public void ThenFavoritePlayersShouldAppearAtTheTopOfTheList()
{
    var favoriteTestPlayers = scenarioContext.GetFavoriteTestPlayers();
    
    foreach (var favoritePlayer in favoriteTestPlayers)
    {
        // Verify favorite players are at the top
        VerifyPlayerAtTopOfList(favoritePlayer.Name);
    }
}
```

## Player Model

The `Player` class from the Core project (`PuttPutt.Core.Models.Player`) represents a player in test scenarios:

```csharp
public class Player
{
    public string Name { get; set; }
    public string Color { get; set; }
    public bool IsFavorite { get; set; }
}
```

## Validation and Error Handling

The implementation includes comprehensive validation:

- **Column validation**: Ensures table has exactly the expected columns (Name, Color, Favorite)
- **Required field validation**: Name and Color cannot be empty
- **Duplicate name validation**: Player names must be unique (case-insensitive)
- **Boolean format validation**: Favorite field must be a valid boolean representation
- **Helpful error messages**: Clear descriptions of what went wrong

## Best Practices

### 1. Use in Background Steps

For scenarios that need the same player setup:

```gherkin
Background:
  Given I have the following players in my player list:
    | Name     | Color | Favorite |
    | John Doe | Blue  | Yes      |
    | Jane Doe | Red   | No       |
```

### 2. Combine with Other Shared Steps

The player data works well with other shared setup steps:

```gherkin
Background:
  Given the app is launched
  And I have the following players in my player list:
    | Name     | Color | Favorite |
    | John Doe | Blue  | Yes      |
  And I am on the main menu
```

### 3. Access in Multiple Step Definition Classes

Any step definition class can access the shared player data by injecting `ScenarioContext` and using the helper methods.

## Testing

The implementation includes comprehensive unit tests in `SharedStepDefinitionsTests.cs` that demonstrate:

- Basic functionality with valid data
- Support for different boolean formats
- Validation error scenarios
- Helper method functionality

Run the tests to verify the implementation works correctly:

```bash
dotnet test --filter "SharedStepDefinitionsTests"
```

## Debugging

Use the debug helper method to see what test data is available:

```csharp
Console.WriteLine(scenarioContext.GetTestPlayersDebugInfo());
```

This will output something like:

```
Test Players (4):
  - John Doe (Blue) - Favorite: True
  - Jane Smith (Red) - Favorite: False
  - Bob Johnson (Green) - Favorite: False
  - Alice Brown (Yellow) - Favorite: True
```
