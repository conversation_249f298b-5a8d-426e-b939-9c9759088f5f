@course-management
Feature: Course Management
  As a putt-putt facility manager or player
  I want to manage different courses and their configurations
  So that games can be played with accurate par values and course-specific settings

  Background:
    Given the app is launched
    And I am on the main menu

  @smoke @happy-path
  Scenario: Create a new course
    Given I navigate to the course management screen
    When I tap "Add New Course"
    And I enter "Adventure Course" as the course name
    And I set the number of holes to 18
    And I set par values for all 18 holes
    And I tap "Save Course"
    Then the course "Adventure Course" should be added to the course list
    And the course should have 18 holes with the specified par values

  @happy-path
  Scenario: Edit an existing course
    Given I have a course "Main Course" with 9 holes
    And I am on the course management screen
    When I tap on "Main Course"
    And I change the course name to "Classic Course"
    And I modify the par value for hole 3 from 3 to 4
    And I tap "Save Changes"
    Then the course should be updated to "Classic Course"
    And hole 3 should have a par value of 4

  @happy-path
  Scenario: Delete a course
    Given I have a course "Old Course" in the course list
    And the course is not currently being used in any active games
    And I am on the course management screen
    When I tap on "Old Course"
    And I tap "Delete Course"
    And I confirm the deletion
    Then "Old Course" should be removed from the course list

  @qr-code @happy-path
  Scenario: Generate QR code for course configuration
    Given I have a course "Adventure Course" with complete configuration
    And I am on the course management screen
    When I tap on "Adventure Course"
    And I tap "Generate QR Code"
    Then a QR code should be generated containing the course configuration
    And I should be able to share or save the QR code
    And the QR code should include course name, hole count, and par values

  @qr-code @happy-path
  Scenario: Import course configuration from QR code
    Given I am on the course management screen
    When I tap "Import from QR Code"
    And I scan a valid course QR code
    Then the course configuration should be imported
    And I should see a preview of the course details
    And I should be able to save the imported course

  @location-based @happy-path
  Scenario: Download course based on location proximity
    Given location services are enabled
    And I am within 100 meters of "Sunny Valley Putt-Putt"
    When I navigate to the course management screen
    Then I should see a notification "Course available for download"
    When I tap "Download Course"
    Then the "Sunny Valley Putt-Putt" course should be downloaded
    And the course should appear in my course list

  @validation @error-handling
  Scenario: Cannot create course with empty name
    Given I am on the course management screen
    When I tap "Add New Course"
    And I leave the course name field empty
    And I set the number of holes to 9
    And I tap "Save Course"
    Then I should see an error message "Course name is required"
    And the course should not be saved

  @validation @error-handling
  Scenario: Cannot create course with invalid hole count
    Given I am on the course management screen
    When I tap "Add New Course"
    And I enter "Test Course" as the course name
    And I set the number of holes to 0
    And I tap "Save Course"
    Then I should see an error message "Number of holes must be between 1 and 50"
    And the course should not be saved

  @validation @error-handling
  Scenario: Cannot delete course that is in use
    Given I have a course "Active Course" 
    And there is an active game using "Active Course"
    And I am on the course management screen
    When I tap on "Active Course"
    And I tap "Delete Course"
    Then I should see an error message "Cannot delete course that is currently in use"
    And the course should not be deleted

  @validation @error-handling
  Scenario: Cannot create duplicate course names
    Given I have a course "Main Course" in the course list
    And I am on the course management screen
    When I tap "Add New Course"
    And I enter "Main Course" as the course name
    And I set the number of holes to 18
    And I tap "Save Course"
    Then I should see an error message "A course with this name already exists"
    And the duplicate course should not be saved

  @qr-code @error-handling
  Scenario: Handle invalid QR code during import
    Given I am on the course management screen
    When I tap "Import from QR Code"
    And I scan an invalid or corrupted QR code
    Then I should see an error message "Invalid course QR code"
    And no course should be imported

  @location-based @error-handling
  Scenario: Handle location services disabled
    Given location services are disabled
    When I navigate to the course management screen
    Then I should not see any location-based course suggestions
    And I should see a message "Enable location services to discover nearby courses"

  @edge-case
  Scenario: Create course with maximum number of holes
    Given I am on the course management screen
    When I tap "Add New Course"
    And I enter "Marathon Course" as the course name
    And I set the number of holes to 50
    And I set par values for all 50 holes
    And I tap "Save Course"
    Then the course should be created successfully
    And all 50 holes should have their par values saved
