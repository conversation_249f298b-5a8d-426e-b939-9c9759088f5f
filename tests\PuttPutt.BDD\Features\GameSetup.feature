@game-setup
Feature: Game Setup
  As a putt-putt player
  I want to set up new games with selected players and courses
  So that I can start playing and tracking scores

  Background:
    Given the app is launched
    And I have players "<PERSON>", "<PERSON>", and "<PERSON>" in my player list

  @smoke @happy-path
  Scenario: Create a new game with existing players
    Given I am on the main menu
    When I tap "New Game"
    And I select players "John Doe" and "<PERSON>"
    And I choose "18 holes" for the game
    And I tap "Start Game"
    Then a new game should be created
    And the game should include "John Doe" and "Jane Smith"
    And the game should be set for 18 holes
    And I should be taken to the scoring screen

  @happy-path
  Scenario: Create a game with guest players
    Given I am on the main menu
    When I tap "New Game"
    And I select player "<PERSON>"
    And I tap "Add Guest Player"
    And I enter "Guest Player 1" as the guest name
    And I choose "9 holes" for the game
    And I tap "Start Game"
    Then a new game should be created
    And the game should include "John Doe" and "Guest Player 1"
    And the guest player should not be saved to the player list
    And the game should be set for 9 holes

  @happy-path
  Scenario: Create a game with custom number of holes
    Given I am on the main menu
    When I tap "New Game"
    And I select players "<PERSON>" and "<PERSON>"
    And I choose "Custom" for the number of holes
    And I enter "12" as the custom hole count
    And I tap "Start Game"
    Then a new game should be created
    And the game should be set for 12 holes

  @course-management @happy-path
  Scenario: Create a game with a predefined course
    Given I have a course "Adventure Course" with 18 holes and par values
    And I am on the main menu
    When I tap "New Game"
    And I select players "John Doe" and "Jane Smith"
    And I select "Adventure Course" from the course list
    And I tap "Start Game"
    Then a new game should be created
    And the game should use the "Adventure Course" settings
    And each hole should have the predefined par values

  @course-management @qr-code
  Scenario: Set up game using QR code course configuration
    Given I am on the main menu
    When I tap "New Game"
    And I select players "John Doe" and "Bob Johnson"
    And I tap "Scan Course QR Code"
    And I scan a valid course QR code for "Main Course"
    Then the course should be automatically configured
    And the course name should be set to "Main Course"
    And the hole count and par values should be loaded from the QR code
    And I should be able to proceed with "Start Game"

  @validation @error-handling
  Scenario: Cannot start game without selecting players
    Given I am on the main menu
    When I tap "New Game"
    And I do not select any players
    And I tap "Start Game"
    Then I should see an error message "At least one player must be selected"
    And the game should not start

  @validation @error-handling
  Scenario: Cannot start game with invalid custom hole count
    Given I am on the main menu
    When I tap "New Game"
    And I select player "John Doe"
    And I choose "Custom" for the number of holes
    And I enter "0" as the custom hole count
    And I tap "Start Game"
    Then I should see an error message "Number of holes must be between 1 and 50"
    And the game should not start

  @edge-case
  Scenario: Create game with maximum number of players
    Given I have 8 players in my player list
    And I am on the main menu
    When I tap "New Game"
    And I select all 8 players
    And I choose "18 holes" for the game
    And I tap "Start Game"
    Then a new game should be created with all 8 players
    And the scoring interface should accommodate all players

  @edge-case @error-handling
  Scenario: Cannot add more than maximum allowed players
    Given I have 8 players in my player list
    And I am on the main menu
    When I tap "New Game"
    And I select all 8 players
    And I try to add a guest player
    Then I should see a message "Maximum of 8 players allowed per game"
    And the guest player option should be disabled
