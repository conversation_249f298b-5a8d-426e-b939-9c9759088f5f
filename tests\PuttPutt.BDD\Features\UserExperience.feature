@user-experience
Feature: User Experience and Usability
  As a putt-putt player
  I want the app to be intuitive and work reliably
  So that I can focus on enjoying the game without technical distractions

  Background:
    Given the app is launched

  @offline @smoke @happy-path
  Scenario: Use app without internet connection
    Given I have no internet connection
    And I have previously saved player profiles and courses
    When I start a new game
    And I select players and a course
    And I begin entering scores
    Then the app should function normally
    And all scores should be saved locally
    And I should not see any connectivity error messages

  @responsive-ui @happy-path
  Scenario: App adapts to phone screen orientation
    Given I am using the app on a phone
    And I am on the scoring screen
    When I rotate the device to landscape mode
    Then the scoring interface should adapt to landscape layout
    And all buttons should remain easily accessible
    And the current player and hole information should be clearly visible

  @responsive-ui @happy-path
  Scenario: App works well on tablet screens
    Given I am using the app on a tablet
    When I navigate through different screens
    Then the interface should utilize the larger screen space effectively
    And text and buttons should be appropriately sized
    And the layout should not appear stretched or cramped

  @data-persistence @happy-path
  Scenario: Data persists after app restart
    Given I have player profiles and game history
    When I close the app completely
    And I restart the app
    Then all my player profiles should be available
    And my game history should be intact
    And any saved course configurations should be preserved

  @undo @happy-path
  Scenario: Undo last score entry
    Given I am in an active game
    And I have just entered a score of 4 for "John Doe" on hole 3
    When I tap the "Undo" button
    Then the score entry should be reversed
    And it should be "<PERSON> Doe"'s turn again on hole 3
    And the undo button should become disabled

  @undo @edge-case
  Scenario: Undo is only available for the most recent action
    Given I am in an active game
    And I have entered scores for "John Doe" and "Jane Smith" on hole 3
    When I tap the "Undo" button
    Then only "<PERSON>"'s score should be undone
    And "John Doe"'s score should remain unchanged
    And it should be "Jane Smith"'s turn again

  @share @happy-path
  Scenario: Share final scorecard as text
    Given I have completed a game
    And I am on the final scorecard screen
    When I tap "Share Results"
    And I select "Share as Text"
    Then a sharing dialog should open
    And the text should include all players' names and scores
    And the text should include the course name and date
    And I should be able to share via messaging apps

  @share @happy-path
  Scenario: Share scorecard as image
    Given I have completed a game
    And I am on the final scorecard screen
    When I tap "Share Results"
    And I select "Share as Image"
    Then a scorecard image should be generated
    And the image should include all game details formatted nicely
    And I should be able to share the image via social media or messaging

  @visual-feedback @happy-path
  Scenario: Visual feedback for successful score entry
    Given I am on the scoring screen
    When I enter a score for the current player
    Then I should see a brief animation confirming the entry
    And the score should be highlighted momentarily
    And there should be a subtle sound effect (if sound is enabled)

  @visual-feedback @special-events
  Scenario: Special celebration for hole-in-one
    Given I am on the scoring screen
    When I enter a score of 1 for any player
    Then I should see a "Hole-in-One!" celebration animation
    And there should be a special sound effect
    And the celebration should last for 2-3 seconds
    And confetti or similar visual effects should appear

  @accessibility @happy-path
  Scenario: App supports accessibility features
    Given I have accessibility features enabled on my device
    When I navigate through the app
    Then all buttons and text should be readable by screen readers
    And the app should support voice-over navigation
    And color-blind friendly colors should be used throughout

  @performance @happy-path
  Scenario: App responds quickly to user interactions
    Given I am using the app on a standard device
    When I tap any button or navigate between screens
    Then the response should be immediate (under 200ms)
    And animations should be smooth
    And there should be no noticeable lag

  @error-handling @network
  Scenario: Handle network interruption gracefully
    Given I am using location-based features
    And I lose internet connection during the process
    When the network operation fails
    Then I should see a clear error message
    And the app should offer to retry when connection is restored
    And the app should not crash or freeze

  @error-handling @storage
  Scenario: Handle insufficient storage space
    Given my device has very low storage space
    When I try to save a game or add new data
    And the save operation fails due to storage
    Then I should see a clear error message about storage space
    And I should be given options to free up space or continue without saving
    And the app should not crash

  @battery-optimization
  Scenario: App is battery efficient during long games
    Given I am playing a long 18-hole game
    When the game lasts for over an hour
    Then the app should not drain the battery excessively
    And the screen should dim appropriately during idle periods
    And background processes should be minimized

  @cross-platform @android
  Scenario: App works consistently on Android devices
    Given I am using an Android device
    When I use all major app features
    Then the app should behave identically to other platforms
    And Android-specific UI patterns should be followed
    And the back button should work appropriately

  @cross-platform @ios
  Scenario: App works consistently on iOS devices
    Given I am using an iOS device
    When I use all major app features
    Then the app should behave identically to other platforms
    And iOS-specific UI patterns should be followed
    And navigation should follow iOS conventions
