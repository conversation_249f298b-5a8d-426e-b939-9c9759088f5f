@rules-explainer
Feature: Mini-wiki Rule Explainer
  As a putt-putt player
  I want to access game rules and explanations
  So that I can understand how to play and resolve any disputes

  Background:
    Given the app is launched
    And I am on the main menu

  @smoke @happy-path
  Scenario: Access general game rules
    Given I am on the main menu
    When I tap "Game Rules"
    Then I should see the rules menu
    And I should see an option for "General Course Rules"
    When I tap "General Course Rules"
    Then I should see the general putt-putt rules displayed
    And the rules should be clearly formatted and easy to read

  @happy-path
  Scenario: Navigate through different rule sections
    Given I am viewing the game rules
    When I see the table of contents
    Then I should see sections like "Basic Gameplay", "Scoring", "Etiquette"
    When I tap on "Scoring" section
    Then I should be taken to the scoring rules section
    And I should see detailed information about how scoring works

  @happy-path
  Scenario: Search for specific rules
    Given I am viewing the game rules
    When I tap the search icon
    And I enter "maximum strokes" in the search field
    Then I should see search results related to stroke limits
    And the relevant text should be highlighted
    And I should be able to tap a result to jump to that section

  @happy-path
  Scenario: View rules while in an active game
    Given I have an active game in progress
    And I am on the scoring screen
    When I tap the menu button
    And I select "View Rules"
    Then the rules should open in an overlay or new screen
    And I should be able to return to my game easily
    And my game progress should be preserved

  @happy-path
  Scenario: Access course-specific rules
    Given I have selected a specific course for my game
    And the course has custom rules or notes
    When I tap "Course Rules" during game setup
    Then I should see rules specific to the selected course
    And I should see any special hole instructions or hazards
    And I should see how these rules differ from general rules

  @bookmark @happy-path
  Scenario: Bookmark frequently referenced rules
    Given I am viewing a specific rule section
    When I tap the bookmark icon
    Then the section should be added to my bookmarks
    And I should see a confirmation "Rule bookmarked"
    When I go to "Bookmarked Rules"
    Then I should see the bookmarked section in my list

  @offline @happy-path
  Scenario: Access rules without internet connection
    Given I have no internet connection
    When I tap "Game Rules"
    Then I should still be able to access all general rules
    And all text and images should load properly
    And the search functionality should work offline

  @visual-aids @happy-path
  Scenario: View rules with visual examples
    Given I am viewing the rules section
    When I navigate to "Common Situations"
    Then I should see diagrams or illustrations
    And each visual should have a clear caption
    And the visuals should help explain complex rules

  @sharing @happy-path
  Scenario: Share specific rule with other players
    Given I am viewing a specific rule section
    When I tap the share button
    Then I should be able to share the rule text
    And I should be able to send it via messaging or email
    And the shared content should include the rule title and text

  @feedback @happy-path
  Scenario: Provide feedback on rule clarity
    Given I am viewing a specific rule
    When I scroll to the bottom of the rule section
    Then I should see a "Was this helpful?" section
    And I should be able to rate the rule explanation
    And I should be able to provide optional feedback comments

  @updates @happy-path
  Scenario: Receive rule updates when available
    Given I have internet connection
    And there are updated rules available
    When I open the rules section
    Then I should see a notification about rule updates
    And I should be able to download the updated rules
    And I should see what has changed in the updates

  @accessibility @happy-path
  Scenario: Rules are accessible to all users
    Given I have accessibility features enabled
    When I view the game rules
    Then all text should be readable by screen readers
    And I should be able to adjust text size
    And high contrast mode should be supported
    And images should have descriptive alt text

  @quick-reference @happy-path
  Scenario: Access quick reference during disputes
    Given I am in an active game
    And there is a question about a rule
    When I tap "Quick Reference"
    Then I should see a condensed version of key rules
    And I should see common situations and their resolutions
    And I should be able to find answers quickly without extensive reading

  @history @happy-path
  Scenario: View recently accessed rules
    Given I have viewed several rule sections
    When I tap "Recently Viewed"
    Then I should see a list of the last 5 rule sections I accessed
    And I should be able to quickly return to any of them
    And the list should be ordered by most recent first

  @error-handling
  Scenario: Handle missing rule content gracefully
    Given a specific rule section fails to load
    When I try to access that section
    Then I should see an error message "Rule content unavailable"
    And I should see an option to "Try Again"
    And I should be able to access other rule sections normally
